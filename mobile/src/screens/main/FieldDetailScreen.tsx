import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
  TextInput,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { fieldService, Field, FieldWithDetails, SoilSample, Task, FieldNote } from '@/services/fieldService';
import { useAuth } from '@/store/AuthProvider';

type FieldDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'FieldDetail'>;

// Types are imported from fieldService

const FieldDetailScreen: React.FC<FieldDetailScreenProps> = ({ route, navigation }) => {
  const { fieldId } = route.params;
  const [field, setField] = useState<Field | null>(null);
  const [soilSamples, setSoilSamples] = useState<SoilSample[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Field Notes state
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [currentNote, setCurrentNote] = useState<FieldNote | null>(null);
  const [noteText, setNoteText] = useState('');
  const [notePhotoUri, setNotePhotoUri] = useState<string | undefined>(undefined);

  const { user } = useAuth();

  useEffect(() => {
    const fetchFieldData = async () => {
      try {
        setLoading(true);

        // Fetch field details with soil samples and tasks
        const fieldDetails = await fieldService.getFieldById(fieldId);

        if (fieldDetails) {
          setField(fieldDetails);
          setSoilSamples(fieldDetails.soilSamples || []);
          setTasks(fieldDetails.tasks || []);
        } else {
          // If field details couldn't be fetched, try to get individual data
          console.warn(`Could not fetch complete field details for field ${fieldId}, trying individual endpoints`);

          // Try to get field data
          const fieldData = await fieldService.getFieldById(fieldId);
          if (fieldData) {
            setField(fieldData);
          } else {
            console.error(`Could not fetch field data for field ${fieldId}`);
            Alert.alert('Error', 'Could not load field data. Please try again later.');
          }

          // Try to get soil samples
          const soilSamplesData = await fieldService.getSoilSamples(fieldId);
          setSoilSamples(soilSamplesData);

          // Try to get tasks
          const tasksData = await fieldService.getFieldTasks(fieldId);
          setTasks(tasksData);
        }
      } catch (error) {
        console.error('Error fetching field data:', error);
        Alert.alert('Error', 'Could not load field data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (fieldId) {
      fetchFieldData();
    }
  }, [fieldId]);

  const handleEditField = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Field', 'Field editing functionality would be implemented here.');
  };

  const handleAddSoilSample = () => {
    // In a real app, this would navigate to a soil sample creation screen
    Alert.alert('Add Soil Sample', 'Soil sample creation functionality would be implemented here.');
  };

  const handleAddTask = () => {
    // In a real app, this would navigate to a task creation screen
    Alert.alert('Add Task', 'Task creation functionality would be implemented here.');
  };

  const handleViewOnMap = () => {
    navigation.navigate('Map');
  };

  // Field Notes handlers
  const handleAddNote = () => {
    setCurrentNote(null);
    setNoteText('');
    setNotePhotoUri(undefined);
    setIsNoteModalVisible(true);
  };

  const handleEditNote = (note: FieldNote) => {
    setCurrentNote(note);
    setNoteText(note.text);
    setNotePhotoUri(note.photoUri);
    setIsNoteModalVisible(true);
  };

  const handleDeleteNote = (noteId: string) => {
    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            if (field && field.fieldNotes) {
              const updatedNotes = field.fieldNotes.filter(note => note.id !== noteId);
              setField({
                ...field,
                fieldNotes: updatedNotes,
              });
            }
          },
        },
      ],
    );
  };

  const handleSaveNote = () => {
    if (!field) return;

    if (noteText.trim() === '') {
      Alert.alert('Error', 'Note text cannot be empty');
      return;
    }

    const now = new Date();
    const formattedDate = now.toISOString().split('T')[0];

    if (currentNote) {
      // Edit existing note
      const updatedNotes = field.fieldNotes?.map(note => 
        note.id === currentNote.id 
          ? { ...note, text: noteText, photoUri: notePhotoUri }
          : note
      ) || [];

      setField({
        ...field,
        fieldNotes: updatedNotes,
      });
    } else {
      // Add new note
      const newNote: FieldNote = {
        id: Date.now().toString(),
        text: noteText,
        date: formattedDate,
        photoUri: notePhotoUri,
      };

      setField({
        ...field,
        fieldNotes: [...(field.fieldNotes || []), newNote],
      });
    }

    setIsNoteModalVisible(false);
    setCurrentNote(null);
    setNoteText('');
    setNotePhotoUri(undefined);
  };

  const handleTakePhoto = () => {
    // In a real app, this would use the camera API
    Alert.alert('Take Photo', 'Camera functionality would be implemented here.');
    // For demo purposes, we'll just set a placeholder image
    setNotePhotoUri('https://via.placeholder.com/300x200?text=New+Photo');
  };

  const handleRemovePhoto = () => {
    setNotePhotoUri(undefined);
  };

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Field Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Size:</Text>
          <Text style={styles.infoValue}>{field?.acres} acres</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Current Crop:</Text>
          <Text style={styles.infoValue}>{field?.crop || 'None'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Status:</Text>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: field?.status === 'Active' ? '#22c55e' : '#d1d5db' }
            ]} />
            <Text style={styles.statusText}>{field?.status}</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Soil Type:</Text>
          <Text style={styles.infoValue}>{field?.soilType || 'Unknown'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Planting Date:</Text>
          <Text style={styles.infoValue}>{field?.plantingDate || 'Not set'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Harvest Date:</Text>
          <Text style={styles.infoValue}>{field?.harvestDate || 'Not set'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Expected Yield:</Text>
          <Text style={styles.infoValue}>
            {field?.expectedYield ? `${field.expectedYield} ${field.yieldUnit}` : 'Not set'}
          </Text>
        </View>
      </View>

      {field?.notes && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Notes</Text>
          <Text style={styles.notesText}>{field.notes}</Text>
        </View>
      )}

      <View style={styles.infoCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Recent Tasks</Text>
          <TouchableOpacity onPress={handleAddTask}>
            <Text style={styles.cardAction}>Add Task</Text>
          </TouchableOpacity>
        </View>
        {tasks.length > 0 ? (
          tasks.map(task => (
            <View key={task.id} style={styles.taskItem}>
              <View style={styles.taskHeader}>
                <Text style={styles.taskTitle}>{task.title}</Text>
                <View style={[
                  styles.taskStatusBadge,
                  { backgroundColor: task.status === 'Pending' ? '#fef3c7' : '#dbeafe' }
                ]}>
                  <Text style={styles.taskStatusText}>{task.status}</Text>
                </View>
              </View>
              <View style={styles.taskDetails}>
                <Text style={styles.taskDate}>Due: {task.dueDate}</Text>
                {task.assignedTo && (
                  <Text style={styles.taskAssignee}>Assigned to: {task.assignedTo}</Text>
                )}
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No tasks for this field</Text>
        )}
      </View>

      <TouchableOpacity style={styles.mapCard} onPress={handleViewOnMap}>
        <Image
          source={{ uri: 'https://via.placeholder.com/400x200?text=Field+Map' }}
          style={styles.mapImage}
        />
        <View style={styles.mapOverlay}>
          <Text style={styles.mapText}>View on Map</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderSoilTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Soil Samples</Text>
          <TouchableOpacity onPress={handleAddSoilSample}>
            <Text style={styles.cardAction}>Add Sample</Text>
          </TouchableOpacity>
        </View>
        {soilSamples.length > 0 ? (
          soilSamples.map(sample => (
            <View key={sample.id} style={styles.soilSampleItem}>
              <Text style={styles.soilSampleDate}>Sample Date: {sample.date}</Text>
              <View style={styles.soilDataGrid}>
                <View style={styles.soilDataItem}>
                  <Text style={styles.soilDataLabel}>pH</Text>
                  <Text style={styles.soilDataValue}>{sample.pH}</Text>
                </View>
                <View style={styles.soilDataItem}>
                  <Text style={styles.soilDataLabel}>Organic Matter</Text>
                  <Text style={styles.soilDataValue}>{sample.organicMatter}%</Text>
                </View>
                <View style={styles.soilDataItem}>
                  <Text style={styles.soilDataLabel}>Nitrogen (N)</Text>
                  <Text style={styles.soilDataValue}>{sample.nitrogen} ppm</Text>
                </View>
                <View style={styles.soilDataItem}>
                  <Text style={styles.soilDataLabel}>Phosphorus (P)</Text>
                  <Text style={styles.soilDataValue}>{sample.phosphorus} ppm</Text>
                </View>
                <View style={styles.soilDataItem}>
                  <Text style={styles.soilDataLabel}>Potassium (K)</Text>
                  <Text style={styles.soilDataValue}>{sample.potassium} ppm</Text>
                </View>
              </View>
              {sample.notes && (
                <Text style={styles.soilSampleNotes}>{sample.notes}</Text>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No soil samples for this field</Text>
        )}
      </View>
    </View>
  );

  const renderNotesTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Field Notes</Text>
          <TouchableOpacity onPress={handleAddNote}>
            <Text style={styles.cardAction}>Add Note</Text>
          </TouchableOpacity>
        </View>

        {field?.fieldNotes && field.fieldNotes.length > 0 ? (
          field.fieldNotes.map((note) => (
            <View key={note.id} style={styles.noteItem}>
              <View style={styles.noteHeader}>
                <Text style={styles.noteDate}>{note.date}</Text>
                <View style={styles.noteActions}>
                  <TouchableOpacity 
                    style={styles.noteActionButton} 
                    onPress={() => handleEditNote(note)}
                  >
                    <Ionicons name="create-outline" size={18} color="#22c55e" />
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.noteActionButton} 
                    onPress={() => handleDeleteNote(note.id)}
                  >
                    <Ionicons name="trash-outline" size={18} color="#ef4444" />
                  </TouchableOpacity>
                </View>
              </View>

              <Text style={styles.noteText}>{note.text}</Text>

              {note.photoUri && (
                <View style={styles.noteImageContainer}>
                  <Image 
                    source={{ uri: note.photoUri }} 
                    style={styles.noteImage} 
                    resizeMode="cover"
                  />
                </View>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>No notes for this field. Add a note to track observations.</Text>
        )}
      </View>
    </View>
  );

  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Field History</Text>
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <View style={styles.timelineContent}>
            <Text style={styles.timelineDate}>September 10, 2023</Text>
            <Text style={styles.timelineTitle}>Irrigation System Maintenance</Text>
            <Text style={styles.timelineDescription}>
              Repaired sprinkler heads in the northwest section of the field.
            </Text>
          </View>
        </View>
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <View style={styles.timelineContent}>
            <Text style={styles.timelineDate}>August 15, 2023</Text>
            <Text style={styles.timelineTitle}>Pest Control Application</Text>
            <Text style={styles.timelineDescription}>
              Applied insecticide to control corn borer infestation.
            </Text>
          </View>
        </View>
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <View style={styles.timelineContent}>
            <Text style={styles.timelineDate}>July 20, 2023</Text>
            <Text style={styles.timelineTitle}>Fertilizer Application</Text>
            <Text style={styles.timelineDescription}>
              Applied nitrogen fertilizer at 150 lbs/acre.
            </Text>
          </View>
        </View>
        <View style={styles.timelineItem}>
          <View style={styles.timelineDot} />
          <View style={styles.timelineContent}>
            <Text style={styles.timelineDate}>{field?.plantingDate}</Text>
            <Text style={styles.timelineTitle}>Planting</Text>
            <Text style={styles.timelineDescription}>
              Planted {field?.crop} at 32,000 seeds/acre.
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading field details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.fieldName}>{field?.name}</Text>
          <Text style={styles.fieldDetails}>
            {field?.acres} acres • {field?.crop || 'No crop'}
          </Text>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={handleEditField}>
          <Ionicons name="create-outline" size={24} color="#22c55e" />
        </TouchableOpacity>
      </View>

      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'overview' && styles.activeTabButton]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'overview' && styles.activeTabButtonText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'soil' && styles.activeTabButton]}
          onPress={() => setActiveTab('soil')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'soil' && styles.activeTabButtonText]}>
            Soil
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'notes' && styles.activeTabButton]}
          onPress={() => setActiveTab('notes')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'notes' && styles.activeTabButtonText]}>
            Notes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'history' && styles.activeTabButton]}
          onPress={() => setActiveTab('history')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'history' && styles.activeTabButtonText]}>
            History
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.tabButton}
          onPress={() => navigation.navigate('CropScouting', { fieldId })}
        >
          <Text style={styles.tabButtonText}>
            Scouting
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'soil' && renderSoilTab()}
        {activeTab === 'notes' && renderNotesTab()}
        {activeTab === 'history' && renderHistoryTab()}
      </ScrollView>

      {/* Note Editing Modal */}
      <Modal
        visible={isNoteModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsNoteModalVisible(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentNote ? 'Edit Note' : 'Add Note'}
              </Text>
              <TouchableOpacity
                onPress={() => setIsNoteModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.noteInput}
              placeholder="Enter your observations..."
              value={noteText}
              onChangeText={setNoteText}
              multiline={true}
              autoFocus={true}
            />

            {notePhotoUri ? (
              <View style={styles.photoPreviewContainer}>
                <Image
                  source={{ uri: notePhotoUri }}
                  style={styles.photoPreview}
                  resizeMode="cover"
                />
                <TouchableOpacity
                  style={styles.removePhotoButton}
                  onPress={handleRemovePhoto}
                >
                  <Ionicons name="close-circle" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.photoButton}
                onPress={handleTakePhoto}
              >
                <Ionicons name="camera-outline" size={24} color="#22c55e" />
                <Text style={styles.photoButtonText}>Add Photo</Text>
              </TouchableOpacity>
            )}

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setIsNoteModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveNote}
              >
                <Text style={styles.saveButtonText}>Save Note</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flex: 1,
  },
  fieldName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  fieldDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#22c55e',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabButtonText: {
    color: '#22c55e',
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    padding: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  cardAction: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  taskItem: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  taskStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  taskStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  taskDetails: {
    marginTop: 5,
  },
  taskDate: {
    fontSize: 12,
    color: '#666',
  },
  taskAssignee: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 10,
  },
  mapCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  mapImage: {
    width: '100%',
    height: 200,
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 10,
    alignItems: 'center',
  },
  mapText: {
    color: '#fff',
    fontWeight: '500',
  },
  soilSampleItem: {
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  soilSampleDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 10,
  },
  soilDataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  soilDataItem: {
    width: '50%',
    paddingHorizontal: 5,
    marginBottom: 10,
  },
  soilDataLabel: {
    fontSize: 12,
    color: '#666',
  },
  soilDataValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  soilSampleNotes: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
    fontStyle: 'italic',
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#22c55e',
    marginTop: 5,
    marginRight: 10,
  },
  timelineContent: {
    flex: 1,
  },
  timelineDate: {
    fontSize: 12,
    color: '#666',
  },
  timelineTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginVertical: 2,
  },
  timelineDescription: {
    fontSize: 14,
    color: '#666',
  },
  // Field Notes styles
  noteItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteDate: {
    fontSize: 12,
    color: '#666',
  },
  noteActions: {
    flexDirection: 'row',
  },
  noteActionButton: {
    padding: 5,
    marginLeft: 10,
  },
  noteText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 10,
  },
  noteImageContainer: {
    marginTop: 5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  noteImage: {
    width: '100%',
    height: 150,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 15,
  },
  photoPreviewContainer: {
    position: 'relative',
    marginBottom: 15,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photoPreview: {
    width: '100%',
    height: 200,
  },
  removePhotoButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
  },
  photoButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#22c55e',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
  },
  saveButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#22c55e',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});

export default FieldDetailScreen;
