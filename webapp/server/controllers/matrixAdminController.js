import axios from 'axios';
import dotenv from 'dotenv';
import User from '../models/User.js';
import crypto from 'crypto';

dotenv.config();

// Matrix server configuration
const MATRIX_SERVER_URL = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
const MATRIX_ADMIN_TOKEN = process.env.MATRIX_ADMIN_TOKEN;

// Helper function to make authenticated requests to the Matrix Admin API
const matrixAdminRequest = async (method, endpoint, data = null) => {
  // Check if MATRIX_ADMIN_TOKEN is defined
  if (!MATRIX_ADMIN_TOKEN) {
    throw new Error('MATRIX_ADMIN_TOKEN is not defined. Please set this environment variable.');
  }

  try {
    const url = `${MATRIX_SERVER_URL}/_synapse/admin/${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${MATRIX_ADMIN_TOKEN}`,
      'Content-Type': 'application/json'
    };

    const response = await axios({
      method,
      url,
      headers,
      data
    });

    return response.data;
  } catch (error) {
    console.error(`Matrix Admin API Error: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}, Data:`, error.response.data);
      throw new Error(`Matrix Admin API Error: ${error.response.data.error || error.message}`);
    }
    throw error;
  }
};

/**
 * Matrix Admin Controller for managing the Matrix chat system
 */
const matrixAdminController = {
  /**
   * Get all Matrix users
   */
  getUsers: async (req, res) => {
    try {
      const { from = 0, limit = 100, guests = false, deactivated = false } = req.query;

      const data = await matrixAdminRequest(
        'GET', 
        `v2/users?from=${from}&limit=${limit}&guests=${guests}&deactivated=${deactivated}`
      );

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix users:', error);
      return res.status(500).json({ error: 'Failed to get Matrix users' });
    }
  },

  /**
   * Get a specific Matrix user
   */
  getUserById: async (req, res) => {
    try {
      const { userId } = req.params;

      // Ensure the userId is properly formatted
      const formattedUserId = userId.startsWith('@') ? userId : `@${userId}:${process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'}`;

      const data = await matrixAdminRequest('GET', `v2/users/${encodeURIComponent(formattedUserId)}`);

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix user:', error);
      return res.status(500).json({ error: 'Failed to get Matrix user' });
    }
  },

  /**
   * Create a new Matrix user
   */
  createUser: async (req, res) => {
    try {
      const { username, password, admin = false, displayname } = req.body;

      if (!username || !password) {
        return res.status(400).json({ error: 'Username and password are required' });
      }

      // Get a nonce for registration
      const nonceResponse = await matrixAdminRequest('GET', 'v1/register');
      const nonce = nonceResponse.nonce;

      // Create HMAC for registration
      const sharedSecret = process.env.REGISTRATION_SHARED_SECRET;

      if (!sharedSecret) {
        return res.status(500).json({ error: 'Registration shared secret is not configured' });
      }

      const hmacContent = `${nonce}\0${username}\0${password}\0${admin ? 'admin' : ''}`;
      const mac = crypto.createHmac('sha1', sharedSecret).update(hmacContent).digest('hex');

      // Register the user
      const registerData = {
        nonce,
        username,
        password,
        admin,
        mac
      };

      const data = await matrixAdminRequest('POST', 'v1/register', registerData);

      // If a display name was provided, set it
      if (displayname && data.access_token) {
        const userId = data.user_id;
        await axios({
          method: 'PUT',
          url: `${MATRIX_SERVER_URL}/_matrix/client/v3/profile/${encodeURIComponent(userId)}/displayname`,
          headers: {
            'Authorization': `Bearer ${data.access_token}`,
            'Content-Type': 'application/json'
          },
          data: { displayname }
        });
      }

      return res.status(201).json(data);
    } catch (error) {
      console.error('Error creating Matrix user:', error);
      return res.status(500).json({ error: 'Failed to create Matrix user' });
    }
  },

  /**
   * Update a Matrix user
   */
  updateUser: async (req, res) => {
    try {
      const { userId } = req.params;
      const { displayname, avatar_url, admin, deactivated, password } = req.body;

      // Ensure the userId is properly formatted
      const formattedUserId = userId.startsWith('@') ? userId : `@${userId}:${process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'}`;

      // Update user admin status if provided
      if (admin !== undefined) {
        await matrixAdminRequest('PUT', `v2/users/${encodeURIComponent(formattedUserId)}/admin`, { admin });
      }

      // Deactivate/reactivate user if provided
      if (deactivated !== undefined) {
        if (deactivated) {
          await matrixAdminRequest('POST', `v1/deactivate/${encodeURIComponent(formattedUserId)}`, { erase: false });
        } else {
          // Reactivation requires a password reset
          if (!password) {
            return res.status(400).json({ error: 'Password is required to reactivate a user' });
          }

          await matrixAdminRequest('POST', `v1/reset_password/${encodeURIComponent(formattedUserId)}`, { 
            new_password: password,
            logout_devices: true
          });
        }
      }

      // Reset password if provided
      if (password && deactivated === undefined) {
        await matrixAdminRequest('POST', `v1/reset_password/${encodeURIComponent(formattedUserId)}`, { 
          new_password: password,
          logout_devices: true
        });
      }

      // Get the updated user data
      const userData = await matrixAdminRequest('GET', `v2/users/${encodeURIComponent(formattedUserId)}`);

      return res.status(200).json(userData);
    } catch (error) {
      console.error('Error updating Matrix user:', error);
      return res.status(500).json({ error: 'Failed to update Matrix user' });
    }
  },

  /**
   * Delete a Matrix user
   */
  deleteUser: async (req, res) => {
    try {
      const { userId } = req.params;
      const { erase = false } = req.query;

      // Ensure the userId is properly formatted
      const formattedUserId = userId.startsWith('@') ? userId : `@${userId}:${process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'}`;

      await matrixAdminRequest('POST', `v1/deactivate/${encodeURIComponent(formattedUserId)}`, { erase: erase === 'true' });

      return res.status(200).json({ message: 'User deactivated successfully' });
    } catch (error) {
      console.error('Error deleting Matrix user:', error);
      return res.status(500).json({ error: 'Failed to delete Matrix user' });
    }
  },

  /**
   * Get all rooms
   */
  getRooms: async (req, res) => {
    try {
      const { from = 0, limit = 100, order_by = 'name' } = req.query;

      const data = await matrixAdminRequest('GET', `v1/rooms?from=${from}&limit=${limit}&order_by=${order_by}`);

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix rooms:', error);
      return res.status(500).json({ error: 'Failed to get Matrix rooms' });
    }
  },

  /**
   * Get a specific room
   */
  getRoomById: async (req, res) => {
    try {
      const { roomId } = req.params;

      const data = await matrixAdminRequest('GET', `v1/rooms/${encodeURIComponent(roomId)}`);

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix room:', error);
      return res.status(500).json({ error: 'Failed to get Matrix room' });
    }
  },

  /**
   * Delete a room
   */
  deleteRoom: async (req, res) => {
    try {
      const { roomId } = req.params;
      const { new_room_id, message = 'This room has been removed by an administrator' } = req.body;

      const data = await matrixAdminRequest('DELETE', `v1/rooms/${encodeURIComponent(roomId)}`, {
        new_room_id,
        message
      });

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error deleting Matrix room:', error);
      return res.status(500).json({ error: 'Failed to delete Matrix room' });
    }
  },

  /**
   * Get room members
   */
  getRoomMembers: async (req, res) => {
    try {
      const { roomId } = req.params;

      const data = await matrixAdminRequest('GET', `v1/rooms/${encodeURIComponent(roomId)}/members`);

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix room members:', error);
      return res.status(500).json({ error: 'Failed to get Matrix room members' });
    }
  },

  /**
   * Get server statistics
   */
  getServerStats: async (req, res) => {
    try {
      const data = await matrixAdminRequest('GET', 'v1/statistics');

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix server statistics:', error);
      // Return a more specific error message to help with debugging
      return res.status(500).json({ 
        error: 'Failed to get Matrix server statistics', 
        details: error.message 
      });
    }
  },

  /**
   * Get server version
   */
  getServerVersion: async (req, res) => {
    try {
      const data = await matrixAdminRequest('GET', 'v1/server_version');

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting Matrix server version:', error);
      // Return a more specific error message to help with debugging
      return res.status(500).json({ 
        error: 'Failed to get Matrix server version', 
        details: error.message 
      });
    }
  },

  /**
   * Purge room history
   */
  purgeRoomHistory: async (req, res) => {
    try {
      const { roomId } = req.params;
      const { delete_local_events = true, purge_up_to_ts } = req.body;

      if (!purge_up_to_ts) {
        return res.status(400).json({ error: 'purge_up_to_ts is required' });
      }

      const data = await matrixAdminRequest('POST', `v1/purge_history/${encodeURIComponent(roomId)}`, {
        delete_local_events,
        purge_up_to_ts
      });

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error purging Matrix room history:', error);
      return res.status(500).json({ error: 'Failed to purge Matrix room history' });
    }
  },

  /**
   * Get user media
   */
  getUserMedia: async (req, res) => {
    try {
      const { userId } = req.params;
      const { limit = 100 } = req.query;

      // Ensure the userId is properly formatted
      const formattedUserId = userId.startsWith('@') ? userId : `@${userId}:${process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'}`;

      const data = await matrixAdminRequest('GET', `v1/users/${encodeURIComponent(formattedUserId)}/media?limit=${limit}`);

      return res.status(200).json(data);
    } catch (error) {
      console.error('Error getting user media:', error);
      return res.status(500).json({ error: 'Failed to get user media' });
    }
  },

  /**
   * Delete user media
   */
  deleteUserMedia: async (req, res) => {
    try {
      const { userId } = req.params;

      // Ensure the userId is properly formatted
      const formattedUserId = userId.startsWith('@') ? userId : `@${userId}:${process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'}`;

      await matrixAdminRequest('DELETE', `v1/users/${encodeURIComponent(formattedUserId)}/media`);

      return res.status(200).json({ message: 'User media deleted successfully' });
    } catch (error) {
      console.error('Error deleting user media:', error);
      return res.status(500).json({ error: 'Failed to delete user media' });
    }
  },

  /**
   * Sync users from nxtacre to matrix
   */
  syncUsers: async (req, res) => {
    try {
      // Get all active users from nxtacre
      const users = await User.findAll({
        where: {
          // Exclude deactivated users if you have a field for that
          // deactivated: false
        },
        attributes: ['id', 'email', 'first_name', 'last_name', 'matrix_token']
      });

      console.log(`Found ${users.length} users in nxtacre to sync with Matrix`);

      // Get existing Matrix users
      const matrixUsersResponse = await matrixAdminRequest('GET', 'v2/users?limit=1000');
      const matrixUsers = matrixUsersResponse.users || [];

      console.log(`Found ${matrixUsers.length} existing users in Matrix`);

      // Create a map of Matrix users by localpart (username)
      const matrixUserMap = {};
      matrixUsers.forEach(user => {
        // Extract localpart from user_id (e.g., @username:domain -> username)
        const localpart = user.name;
        matrixUserMap[localpart] = user;
      });

      // Track sync results
      const results = {
        total: users.length,
        created: 0,
        updated: 0,
        errors: []
      };

      // Process each user
      for (const user of users) {
        try {
          // Check if email exists
          if (!user.email) {
            console.log(`User ${user.id} has no email, skipping`);
            results.errors.push({ user: user.id, error: 'Email is required' });
            continue;
          }

          // Create a username from email (remove @ and domain, replace dots with underscores)
          const email = user.email.toLowerCase();
          const username = email.split('@')[0].replace(/\./g, '_');

          // Check if user already exists in Matrix
          if (matrixUserMap[username]) {
            console.log(`User ${username} already exists in Matrix`);

            // If user exists but we don't have their token, we need to reset their password
            // and get a new token
            if (!user.matrix_token) {
              console.log(`User ${username} exists in Matrix but has no token in nxtacre, resetting password`);

              // Generate a random password
              const password = Math.random().toString(36).substring(2, 15);

              // Reset the user's password
              await matrixAdminRequest('POST', `v1/reset_password/${encodeURIComponent('@' + username + ':' + (process.env.MATRIX_DOMAIN || 'chat.nxtacre.com'))}`, {
                new_password: password,
                logout_devices: false
              });

              // Log in to get a new access token
              try {
                const loginResponse = await axios.post(`${MATRIX_SERVER_URL}/_matrix/client/v3/login`, {
                  type: 'm.login.password',
                  identifier: {
                    type: 'm.id.user',
                    user: username
                  },
                  password: password
                });

                // Store the access token in the user record
                if (loginResponse.data && loginResponse.data.access_token) {
                  await user.update({ matrix_token: loginResponse.data.access_token });
                  console.log(`Updated matrix_token for user ${username}`);
                  results.updated++;
                }
              } catch (loginError) {
                console.error(`Error logging in to Matrix for user ${username}:`, loginError);
                results.errors.push({ user: username, error: 'Failed to login after password reset' });
              }
            } else {
              // User exists and we have their token, nothing to do
              console.log(`User ${username} exists in Matrix and has a token in nxtacre`);
            }
          } else {
            // User doesn't exist in Matrix, create them
            console.log(`Creating new Matrix user for ${username}`);

            // Generate a random password
            const password = Math.random().toString(36).substring(2, 15);

            // Create display name from first and last name
            const displayname = `${user.first_name} ${user.last_name}`;

            // Set admin status (default to false)
            const admin = false;

            // Get a nonce for registration
            const nonceResponse = await matrixAdminRequest('GET', 'v1/register');
            const nonce = nonceResponse.nonce;

            // Create HMAC for registration
            const sharedSecret = process.env.REGISTRATION_SHARED_SECRET;

            if (!sharedSecret) {
              throw new Error('Registration shared secret is not configured');
            }

            const hmacContent = `${nonce}\0${username}\0${password}\0${admin ? 'admin' : 'notadmin'}`;
            const mac = crypto.createHmac('sha1', sharedSecret).update(hmacContent).digest('hex');

            // Register the user
            const registerData = {
              nonce,
              username,
              password,
              admin: false,
              mac
            };

            const registerResponse = await matrixAdminRequest('POST', 'v1/register', registerData);

            // If registration was successful and we got an access token
            if (registerResponse && registerResponse.access_token) {
              // Store the access token in the user record
              await user.update({ matrix_token: registerResponse.access_token });

              // Set the display name
              try {
                await axios({
                  method: 'PUT',
                  url: `${MATRIX_SERVER_URL}/_matrix/client/v3/profile/${encodeURIComponent(registerResponse.user_id)}/displayname`,
                  headers: {
                    'Authorization': `Bearer ${registerResponse.access_token}`,
                    'Content-Type': 'application/json'
                  },
                  data: { displayname }
                });

                console.log(`Created Matrix user ${username} with display name ${displayname}`);
                results.created++;
              } catch (displayNameError) {
                console.error(`Error setting display name for user ${username}:`, displayNameError);
                // We still count this as a success since the user was created
                results.created++;
              }
            }
          }
        } catch (userError) {
          console.error(`Error processing user ${user.email}:`, userError);
          results.errors.push({ user: user.email, error: userError.message });
        }
      }

      return res.status(200).json({
        message: 'User sync completed',
        results
      });
    } catch (error) {
      console.error('Error syncing users to Matrix:', error);
      return res.status(500).json({ error: 'Failed to sync users to Matrix' });
    }
  }
};

export default matrixAdminController;
