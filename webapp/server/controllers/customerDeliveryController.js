import Delivery from '../models/Delivery.js';
import Order from '../models/Order.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Get all deliveries for a customer
export const getCustomerDeliveries = async (req, res) => {
  try {
    // The customer is attached to the request by the authenticateCustomer middleware
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get all deliveries for the customer
    const deliveries = await Delivery.findAll({
      where: { 
        farm_id: farmId,
        customer_id: customer.id
      },
      order: [['scheduled_date', 'DESC']]
    });

    return res.status(200).json({ deliveries });
  } catch (error) {
    console.error('Error getting customer deliveries:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single delivery by ID for a customer
export const getCustomerDeliveryById = async (req, res) => {
  try {
    const { deliveryId } = req.params;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the delivery, ensuring it belongs to this customer
    const delivery = await Delivery.findOne({
      where: {
        id: deliveryId,
        farm_id: farmId,
        customer_id: customer.id
      },
      include: [
        {
          model: Order,
          attributes: ['id', 'order_number', 'status', 'total_amount']
        }
      ]
    });

    if (!delivery) {
      return res.status(404).json({ error: 'Delivery not found' });
    }

    return res.status(200).json({ delivery });
  } catch (error) {
    console.error('Error getting customer delivery:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Request a delivery
export const requestDelivery = async (req, res) => {
  try {
    const { products, preferredDate, notes, deliveryAddress } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Validate required fields
    if (!products || !Array.isArray(products) || products.length === 0) {
      return res.status(400).json({ error: 'At least one product is required' });
    }

    if (!preferredDate) {
      return res.status(400).json({ error: 'Preferred delivery date is required' });
    }

    if (!deliveryAddress) {
      return res.status(400).json({ error: 'Delivery address is required' });
    }

    // TODO: Implement delivery request logic
    // This would typically involve creating a new record in a delivery_requests table
    // For now, we'll just return a success message

    return res.status(200).json({ 
      message: 'Delivery request submitted successfully',
      request: {
        customer: {
          id: customer.id,
          name: customer.name
        },
        products,
        preferredDate,
        notes,
        deliveryAddress
      }
    });
  } catch (error) {
    console.error('Error requesting delivery:', error);
    return res.status(500).json({ error: error.message });
  }
};