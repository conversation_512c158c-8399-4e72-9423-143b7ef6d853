-- Set the search path to the appropriate schema
SET search_path TO site;

-- Seed data for help guides
INSERT INTO help_guides (id, title, slug, content, category, subcategory, tags, is_published, "order", created_at, updated_at)
VALUES
  (uuid_generate_v4(), 'Getting Started with NxtAcre', 'getting-started', 
   '# Getting Started with NxtAcre

Welcome to NxtAcre, your comprehensive farm management platform! This guide will help you get started with the basic features of the platform.

## Setting Up Your Account

1. **Complete Your Profile**: Navigate to the Profile section and ensure all your information is up to date.
2. **Set Up Your Farm**: Go to the Farms section and add your farm details, including location, size, and type.
3. **Invite Team Members**: Add team members to collaborate on your farm management.

## Key Features

- **Dashboard**: Get an overview of your farm''s performance and activities.
- **Field Management**: Track and manage your fields, including planting, harvesting, and maintenance.
- **Equipment Management**: Keep track of your equipment, maintenance schedules, and usage.
- **Inventory Management**: Monitor your inventory levels and transactions.
- **Financial Management**: Track your income, expenses, and generate financial reports.

## Need Help?

If you need assistance, you can:
- Check out our help guides
- Contact support through the support ticket system
- Join our community forum to connect with other farmers

Happy farming!',
   'Getting Started', NULL, ARRAY['getting started', 'introduction', 'overview'], TRUE, 1, NOW(), NOW()),

  (uuid_generate_v4(), 'Managing Your Fields', 'managing-fields', 
   '# Managing Your Fields

Fields are a core component of your farm management in NxtAcre. This guide will help you effectively manage your fields.

## Adding a New Field

1. Navigate to the Fields section
2. Click on "Add New Field"
3. Enter the field details:
   - Name
   - Size
   - Location
   - Soil type
   - Current crop (if any)
4. Save the field

## Field Operations

### Planning Crops
1. Select a field
2. Go to the "Crop Planning" tab
3. Add crops you plan to grow
4. Set planting and harvesting dates

### Recording Activities
1. Select a field
2. Go to the "Activities" tab
3. Click "Add Activity"
4. Select the activity type (planting, fertilizing, harvesting, etc.)
5. Enter the details and save

## Field Monitoring

- Use the Field Health feature to monitor crop health
- Track soil conditions and fertility
- View historical data and trends

## Tips for Effective Field Management

- Regularly update field activities
- Monitor soil health with soil tests
- Use the weather forecast to plan field operations
- Analyze historical data to improve future planning',
   'Fields', 'Management', ARRAY['fields', 'crops', 'management'], TRUE, 1, NOW(), NOW()),

  (uuid_generate_v4(), 'Equipment Maintenance Guide', 'equipment-maintenance', 
   '# Equipment Maintenance Guide

Proper equipment maintenance is essential for efficient farm operations. This guide will help you manage your equipment maintenance in NxtAcre.

## Setting Up Maintenance Schedules

1. Navigate to the Equipment section
2. Select an equipment item
3. Go to the "Maintenance" tab
4. Click "Add Maintenance Schedule"
5. Set up the maintenance details:
   - Type of maintenance
   - Frequency
   - Next due date
   - Notifications

## Recording Maintenance Activities

1. When maintenance is performed, select the equipment
2. Go to the "Maintenance" tab
3. Find the scheduled maintenance
4. Click "Record Maintenance"
5. Enter the details:
   - Date performed
   - Cost
   - Notes
   - Parts replaced
6. Save the record

## Maintenance Alerts

- Set up alerts to notify you when maintenance is due
- Receive notifications via email or in-app
- Configure alert timing (e.g., 1 week before due date)

## Best Practices for Equipment Maintenance

- Follow manufacturer recommendations
- Keep detailed records of all maintenance activities
- Monitor equipment performance for early signs of issues
- Schedule maintenance during off-peak seasons
- Train all operators on proper equipment use',
   'Equipment', 'Maintenance', ARRAY['equipment', 'maintenance', 'repairs'], TRUE, 1, NOW(), NOW()),

  (uuid_generate_v4(), 'Financial Management', 'financial-management', 
   '# Financial Management

Effective financial management is crucial for farm profitability. This guide will help you use NxtAcre''s financial tools.

## Setting Up Your Finances

1. Navigate to the Finances section
2. Connect your bank accounts (optional but recommended)
3. Set up your chart of accounts
4. Define your budget categories

## Tracking Income and Expenses

### Recording Income
1. Go to the Transactions section
2. Click "Add Income"
3. Enter the details:
   - Date
   - Amount
   - Source
   - Category
   - Notes
4. Save the transaction

### Recording Expenses
1. Go to the Transactions section
2. Click "Add Expense"
3. Enter the details:
   - Date
   - Amount
   - Vendor
   - Category
   - Notes
4. Save the transaction

## Generating Financial Reports

1. Go to the Reports section
2. Select the report type:
   - Profit & Loss
   - Balance Sheet
   - Cash Flow
   - Expense by Category
3. Set the date range
4. Generate the report
5. Export or print as needed

## Financial Planning

- Use the Budget tool to plan your finances
- Compare actual vs. budgeted expenses
- Analyze profitability by crop or field
- Track your return on investment

## Tax Preparation

- Categorize transactions for tax purposes
- Generate tax reports
- Export data for your accountant',
   'Finances', 'Management', ARRAY['finances', 'accounting', 'budget'], TRUE, 1, NOW(), NOW()),

  (uuid_generate_v4(), 'Using the Support System', 'using-support-system', 
   '# Using the Support System

The NxtAcre support system is designed to help you get assistance when you need it. This guide explains how to use the support ticket system effectively.

## Creating a Support Ticket

1. Click on the Support icon in the navigation menu
2. Select "Create New Ticket"
3. Fill in the ticket details:
   - Subject: A brief description of your issue
   - Description: Detailed information about your problem
   - Category: Select the appropriate category
   - Priority: Set the urgency level
4. Submit the ticket

## Tracking Your Tickets

1. Go to the Support section
2. View the list of your tickets
3. Check the status:
   - Open: Ticket has been created but not yet addressed
   - In Progress: Support team is working on your issue
   - Resolved: Issue has been resolved
   - Closed: Ticket has been closed

## Adding Comments

1. Open an existing ticket
2. Scroll to the comments section
3. Type your comment
4. Click "Add Comment"

## Best Practices for Support Tickets

- Be specific about your issue
- Include steps to reproduce the problem
- Mention any error messages you received
- Attach screenshots if applicable
- Respond promptly to questions from the support team

## Support Hours

Our support team is available Monday through Friday, 9 AM to 5 PM Eastern Time. Tickets submitted outside these hours will be addressed on the next business day.

## Emergency Support

For urgent issues that require immediate attention, please mark your ticket as "Urgent" and call our emergency support line at (555) 123-4567.',
   'Support', 'Help', ARRAY['support', 'help', 'tickets'], TRUE, 1, NOW(), NOW());

-- Seed data for help tips
INSERT INTO help_tips (id, title, content, page_path, element_selector, position, "order", is_active, created_at, updated_at)
VALUES
  (uuid_generate_v4(), 'Dashboard Overview', 'Welcome to your dashboard! Here you can see an overview of your farm''s key metrics and recent activities.', '/dashboard', '.dashboard-container', 'right', 1, TRUE, NOW(), NOW()),
  
  (uuid_generate_v4(), 'Adding a Field', 'Click here to add a new field to your farm.', '/fields', '.add-field-button', 'right', 1, TRUE, NOW(), NOW()),
  
  (uuid_generate_v4(), 'Equipment Maintenance', 'Don''t forget to schedule regular maintenance for your equipment to keep it running smoothly.', '/equipment', '.equipment-list', 'right', 1, TRUE, NOW(), NOW()),
  
  (uuid_generate_v4(), 'Financial Reports', 'Generate financial reports to track your farm''s performance and profitability.', '/reports', '.financial-reports-section', 'right', 1, TRUE, NOW(), NOW()),
  
  (uuid_generate_v4(), 'Weather Forecast', 'Check the weather forecast to plan your field operations.', '/weather', '.weather-forecast', 'right', 1, TRUE, NOW(), NOW()),
  
  (uuid_generate_v4(), 'Support Help', 'Need help? Create a support ticket to get assistance from our team.', '/support', '.create-ticket-button', 'right', 1, TRUE, NOW(), NOW());

-- Seed data for getting started tasks
INSERT INTO getting_started_tasks (id, title, description, link_path, icon, "order", is_active, user_type, created_at, updated_at)
VALUES
  (uuid_generate_v4(), 'Complete Your Profile', 'Update your profile information to personalize your experience.', '/profile', 'user', 1, TRUE, 'all', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Add Your Farm', 'Set up your farm details to get started with farm management.', '/farms/new', 'farm', 2, TRUE, 'farmer', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Add Your First Field', 'Add a field to start tracking your crops and activities.', '/fields/new', 'field', 3, TRUE, 'farmer', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Add Equipment', 'Add your equipment to track maintenance and usage.', '/equipment/new', 'tractor', 4, TRUE, 'farmer', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Connect Your Bank Account', 'Connect your bank account to track your finances automatically.', '/link-account', 'bank', 5, TRUE, 'all', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Explore the Dashboard', 'Familiarize yourself with the dashboard to see key information at a glance.', '/dashboard', 'dashboard', 6, TRUE, 'all', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Check the Weather', 'View the weather forecast to plan your farm activities.', '/weather', 'cloud', 7, TRUE, 'farmer', NOW(), NOW()),
  
  (uuid_generate_v4(), 'Set Up Inventory', 'Add your inventory items to track stock levels.', '/inventory/new', 'inventory', 8, TRUE, 'farmer', NOW(), NOW());