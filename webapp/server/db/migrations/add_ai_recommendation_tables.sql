-- Add AI recommendation tables for farm management
-- This migration adds tables for harvest timing, field improvement, financial optimization, and yield/profit maximization recommendations

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create ai_harvest_recommendations table
SELECT log_migration_step('Creating ai_harvest_recommendations table');

CREATE TABLE IF NOT EXISTS site.ai_harvest_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_name VARCHAR(255) NOT NULL,
  crop_type VARCHAR(255) NOT NULL,
  harvest_date VARCHAR(100) NOT NULL,
  explanation TEXT NOT NULL,
  factors_considered TEXT NOT NULL,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_implemented BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_harvest_recommendations.id IS 'Unique identifier for the harvest recommendation';
COMMENT ON COLUMN site.ai_harvest_recommendations.farm_id IS 'ID of the farm this recommendation belongs to';
COMMENT ON COLUMN site.ai_harvest_recommendations.field_name IS 'Name of the field this recommendation applies to';
COMMENT ON COLUMN site.ai_harvest_recommendations.crop_type IS 'Type of crop this recommendation applies to';
COMMENT ON COLUMN site.ai_harvest_recommendations.harvest_date IS 'Recommended harvest date or date range';
COMMENT ON COLUMN site.ai_harvest_recommendations.explanation IS 'Detailed explanation of the recommendation';
COMMENT ON COLUMN site.ai_harvest_recommendations.factors_considered IS 'Factors considered in making this recommendation';
COMMENT ON COLUMN site.ai_harvest_recommendations.confidence_score IS 'AI confidence score for this recommendation (0-100)';
COMMENT ON COLUMN site.ai_harvest_recommendations.is_implemented IS 'Whether this recommendation has been implemented';
COMMENT ON COLUMN site.ai_harvest_recommendations.created_at IS 'Timestamp when the recommendation was created';
COMMENT ON COLUMN site.ai_harvest_recommendations.updated_at IS 'Timestamp when the recommendation was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_harvest_recommendations_farm_id_idx ON site.ai_harvest_recommendations(farm_id);
CREATE INDEX IF NOT EXISTS ai_harvest_recommendations_field_name_idx ON site.ai_harvest_recommendations(field_name);
CREATE INDEX IF NOT EXISTS ai_harvest_recommendations_crop_type_idx ON site.ai_harvest_recommendations(crop_type);
CREATE INDEX IF NOT EXISTS ai_harvest_recommendations_is_implemented_idx ON site.ai_harvest_recommendations(is_implemented);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_harvest_recommendations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_harvest_recommendations_timestamp
BEFORE UPDATE ON site.ai_harvest_recommendations
FOR EACH ROW EXECUTE FUNCTION update_ai_harvest_recommendations_updated_at();

-- Step 2: Create ai_field_improvement_recommendations table
SELECT log_migration_step('Creating ai_field_improvement_recommendations table');

CREATE TABLE IF NOT EXISTS site.ai_field_improvement_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  field_name VARCHAR(255) NOT NULL,
  improvement_type VARCHAR(255) NOT NULL,
  recommendation TEXT NOT NULL,
  expected_benefits TEXT NOT NULL,
  implementation_timeline VARCHAR(255) NOT NULL,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_implemented BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_field_improvement_recommendations.id IS 'Unique identifier for the field improvement recommendation';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.farm_id IS 'ID of the farm this recommendation belongs to';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.field_name IS 'Name of the field this recommendation applies to';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.improvement_type IS 'Type of improvement (e.g., Soil Amendment, Drainage, Irrigation)';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.recommendation IS 'Detailed recommendation for field improvement';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.expected_benefits IS 'Expected benefits from implementing this recommendation';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.implementation_timeline IS 'Recommended timeline for implementation';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.confidence_score IS 'AI confidence score for this recommendation (0-100)';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.is_implemented IS 'Whether this recommendation has been implemented';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.created_at IS 'Timestamp when the recommendation was created';
COMMENT ON COLUMN site.ai_field_improvement_recommendations.updated_at IS 'Timestamp when the recommendation was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_field_improvement_recommendations_farm_id_idx ON site.ai_field_improvement_recommendations(farm_id);
CREATE INDEX IF NOT EXISTS ai_field_improvement_recommendations_field_name_idx ON site.ai_field_improvement_recommendations(field_name);
CREATE INDEX IF NOT EXISTS ai_field_improvement_recommendations_improvement_type_idx ON site.ai_field_improvement_recommendations(improvement_type);
CREATE INDEX IF NOT EXISTS ai_field_improvement_recommendations_is_implemented_idx ON site.ai_field_improvement_recommendations(is_implemented);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_field_improvement_recommendations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_field_improvement_recommendations_timestamp
BEFORE UPDATE ON site.ai_field_improvement_recommendations
FOR EACH ROW EXECUTE FUNCTION update_ai_field_improvement_recommendations_updated_at();

-- Step 3: Create ai_financial_recommendations table
SELECT log_migration_step('Creating ai_financial_recommendations table');

CREATE TABLE IF NOT EXISTS site.ai_financial_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  category VARCHAR(255) NOT NULL,
  recommendation TEXT NOT NULL,
  financial_impact TEXT NOT NULL,
  implementation_difficulty VARCHAR(50) NOT NULL,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_implemented BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_financial_recommendations.id IS 'Unique identifier for the financial recommendation';
COMMENT ON COLUMN site.ai_financial_recommendations.farm_id IS 'ID of the farm this recommendation belongs to';
COMMENT ON COLUMN site.ai_financial_recommendations.category IS 'Category of the recommendation (e.g., Cost Reduction, Revenue Increase)';
COMMENT ON COLUMN site.ai_financial_recommendations.recommendation IS 'Detailed financial recommendation';
COMMENT ON COLUMN site.ai_financial_recommendations.financial_impact IS 'Potential financial impact of implementing this recommendation';
COMMENT ON COLUMN site.ai_financial_recommendations.implementation_difficulty IS 'Difficulty level of implementing this recommendation (Easy, Moderate, Complex)';
COMMENT ON COLUMN site.ai_financial_recommendations.confidence_score IS 'AI confidence score for this recommendation (0-100)';
COMMENT ON COLUMN site.ai_financial_recommendations.is_implemented IS 'Whether this recommendation has been implemented';
COMMENT ON COLUMN site.ai_financial_recommendations.created_at IS 'Timestamp when the recommendation was created';
COMMENT ON COLUMN site.ai_financial_recommendations.updated_at IS 'Timestamp when the recommendation was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_financial_recommendations_farm_id_idx ON site.ai_financial_recommendations(farm_id);
CREATE INDEX IF NOT EXISTS ai_financial_recommendations_category_idx ON site.ai_financial_recommendations(category);
CREATE INDEX IF NOT EXISTS ai_financial_recommendations_implementation_difficulty_idx ON site.ai_financial_recommendations(implementation_difficulty);
CREATE INDEX IF NOT EXISTS ai_financial_recommendations_is_implemented_idx ON site.ai_financial_recommendations(is_implemented);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_financial_recommendations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_financial_recommendations_timestamp
BEFORE UPDATE ON site.ai_financial_recommendations
FOR EACH ROW EXECUTE FUNCTION update_ai_financial_recommendations_updated_at();

-- Step 4: Create ai_yield_profit_recommendations table
SELECT log_migration_step('Creating ai_yield_profit_recommendations table');

CREATE TABLE IF NOT EXISTS site.ai_yield_profit_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  category VARCHAR(255) NOT NULL,
  recommendation TEXT NOT NULL,
  yield_impact TEXT NOT NULL,
  profit_impact TEXT NOT NULL,
  implementation_timeframe VARCHAR(255) NOT NULL,
  confidence_score DECIMAL(5, 2) NOT NULL,
  is_implemented BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.ai_yield_profit_recommendations.id IS 'Unique identifier for the yield/profit recommendation';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.farm_id IS 'ID of the farm this recommendation belongs to';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.category IS 'Category of the recommendation (e.g., Crop Selection, Precision Agriculture)';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.recommendation IS 'Detailed yield/profit maximization recommendation';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.yield_impact IS 'Potential impact on yield from implementing this recommendation';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.profit_impact IS 'Potential impact on profit from implementing this recommendation';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.implementation_timeframe IS 'Recommended timeframe for implementation';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.confidence_score IS 'AI confidence score for this recommendation (0-100)';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.is_implemented IS 'Whether this recommendation has been implemented';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.created_at IS 'Timestamp when the recommendation was created';
COMMENT ON COLUMN site.ai_yield_profit_recommendations.updated_at IS 'Timestamp when the recommendation was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS ai_yield_profit_recommendations_farm_id_idx ON site.ai_yield_profit_recommendations(farm_id);
CREATE INDEX IF NOT EXISTS ai_yield_profit_recommendations_category_idx ON site.ai_yield_profit_recommendations(category);
CREATE INDEX IF NOT EXISTS ai_yield_profit_recommendations_implementation_timeframe_idx ON site.ai_yield_profit_recommendations(implementation_timeframe);
CREATE INDEX IF NOT EXISTS ai_yield_profit_recommendations_is_implemented_idx ON site.ai_yield_profit_recommendations(is_implemented);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_yield_profit_recommendations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ai_yield_profit_recommendations_timestamp
BEFORE UPDATE ON site.ai_yield_profit_recommendations
FOR EACH ROW EXECUTE FUNCTION update_ai_yield_profit_recommendations_updated_at();

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_ai_recommendation_tables', 
            'webapp/server/db/migrations/add_ai_recommendation_tables.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify all tables were created
DO $$
DECLARE
    missing_tables TEXT := '';
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_harvest_recommendations') THEN
        missing_tables := missing_tables || 'ai_harvest_recommendations, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_field_improvement_recommendations') THEN
        missing_tables := missing_tables || 'ai_field_improvement_recommendations, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_financial_recommendations') THEN
        missing_tables := missing_tables || 'ai_financial_recommendations, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'ai_yield_profit_recommendations') THEN
        missing_tables := missing_tables || 'ai_yield_profit_recommendations, ';
    END IF;

    IF missing_tables <> '' THEN
        missing_tables := SUBSTRING(missing_tables, 1, LENGTH(missing_tables) - 2);
        RAISE EXCEPTION 'Migration failed. The following tables were not created: %', missing_tables;
    ELSE
        RAISE NOTICE 'Migration successful. All tables were created.';
    END IF;
END $$;
