-- Set the search path to the appropriate schema
SET search_path TO site;

-- <PERSON><PERSON> grants table
CREATE TABLE IF NOT EXISTS site.grants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    external_id VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    agency VARCHAR(255),
    opportunity_number VARCHAR(255),
    category VARCHAR(255),
    eligibility TEXT,
    funding_amount VARCHAR(255),
    close_date TIMESTAMP,
    url VARCHAR(255),
    source VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add index on external_id and source for faster lookups
CREATE INDEX IF NOT EXISTS idx_grants_external_id_source ON site.grants(external_id, source);

-- Add index on category for filtering
CREATE INDEX IF NOT EXISTS idx_grants_category ON site.grants(category);

-- Add index on close_date for filtering
CREATE INDEX IF NOT EXISTS idx_grants_close_date ON site.grants(close_date);

-- Add comment to the table
COMMENT ON TABLE site.grants IS 'Stores grant data from various sources like grants.gov, farmers.gov, and USDA';