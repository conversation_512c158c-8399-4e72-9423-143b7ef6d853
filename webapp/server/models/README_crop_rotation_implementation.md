# Crop Rotation Implementation

This document outlines the implementation of the Crop Rotation feature in the NxtAcre Farm Management Platform.

## Overview

The Crop Rotation feature allows farmers to generate and manage crop rotation plans for their fields. Crop rotation is an essential agricultural practice that helps improve soil health, reduce pest and disease pressure, and optimize nutrient utilization.

## Database Schema

The crop rotation data is stored in the `crop_rotations` table with the following structure:

- `id`: UUID - Primary key
- `farm_id`: UUID - Reference to the farm
- `field_id`: UUID - Reference to the field
- `current_crop`: VARCHAR - The current crop in the field
- `recommended_sequence`: JSONB - Array of crops in the recommended rotation sequence
- `benefits`: JSONB - Array of benefits associated with the rotation plan
- `rotation_years`: INTEGER - Number of years in the rotation cycle
- `soil_health_impact`: VARCHAR - Impact on soil health (positive, neutral, negative)
- `created_at`: TIMESTAMP - Creation timestamp
- `updated_at`: TIMESTAMP - Last update timestamp

## API Endpoints

The following API endpoints are available for crop rotation management:

1. `GET /api/crop-rotations` - Get all crop rotation plans for a farm
   - Query parameters:
     - `farmId` (required): ID of the farm
     - `fieldId` (optional): ID of the field to filter by

2. `GET /api/crop-rotations/:planId` - Get a specific crop rotation plan by ID

3. `POST /api/crop-rotations` - Create a new crop rotation plan
   - Request body:
     - `farmId`: ID of the farm
     - `fieldId`: ID of the field
     - `currentCrop`: Current crop in the field
     - `recommendedSequence`: Array of crops in the rotation sequence
     - `benefits` (optional): Array of benefits
     - `rotationYears` (optional): Number of years in the rotation cycle
     - `soilHealthImpact` (optional): Impact on soil health

4. `POST /api/crop-rotations/generate` - Generate a crop rotation plan using AI
   - Request body:
     - `farmId`: ID of the farm
     - `fieldId`: ID of the field

5. `DELETE /api/crop-rotations/:planId` - Delete a crop rotation plan

## Caching Implementation

To improve performance and reduce database load, a caching layer has been implemented using `node-cache`:

- Crop rotation plans are cached for 10 minutes (600 seconds)
- Cache is invalidated when plans are created, updated, or deleted
- Separate cache keys are used for:
  - All plans for a farm: `rotation_plans_{farmId}`
  - Plans for a specific field: `rotation_plans_{farmId}_{fieldId}`
  - Individual plans: `rotation_plan_{planId}`

## Frontend Implementation

The frontend implementation includes:

1. A component for viewing existing crop rotation plans
2. A form for generating new crop rotation plans
3. Real API integration with fallback to mock data if API calls fail

## Future Improvements

Potential future improvements include:

1. More sophisticated AI algorithms for generating rotation plans based on:
   - Soil test results
   - Climate data
   - Historical yield data
   - Market trends
2. Visual timeline representation of rotation plans
3. Integration with planting schedules and task management
4. Notifications for when it's time to rotate crops