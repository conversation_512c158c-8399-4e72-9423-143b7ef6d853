import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import User from './User.js';
import Farm from './Farm.js';
import Document from './Document.js';

dotenv.config();

const DocumentShare = defineModel('DocumentShare', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Document,
      key: 'id'
    }
  },
  share_token: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_shares',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_shares_document_idx',
      fields: ['document_id']
    },
    {
      name: 'document_shares_token_idx',
      fields: ['share_token']
    },
    {
      name: 'document_shares_expires_idx',
      fields: ['expires_at']
    },
    {
      name: 'document_shares_farm_idx',
      fields: ['farm_id']
    },
    {
      name: 'document_shares_created_by_idx',
      fields: ['created_by']
    }
  ]
});

// Define associations
DocumentShare.belongsTo(Document, { foreignKey: 'document_id', as: 'document' });
Document.hasMany(DocumentShare, { foreignKey: 'document_id', as: 'shares' });

DocumentShare.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
User.hasMany(DocumentShare, { foreignKey: 'created_by', as: 'createdShares' });

DocumentShare.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
Farm.hasMany(DocumentShare, { foreignKey: 'farm_id', as: 'documentShares' });

export default DocumentShare;