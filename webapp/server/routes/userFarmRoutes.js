import express from 'express';
import {
  createUserFarm,
  updateUser<PERSON>arm,
  deleteUserFarm,
  getUserFarmsByUserId,
  getUserFarmsByFarmId
} from '../controllers/userFarmController.js';
import { authenticate, isAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Create a new UserFarm association
router.post('/', authenticate, createUserFarm);

// Update a UserFarm association
router.put('/:userFarmId', authenticate, updateUserFarm);

// Delete a UserFarm association
router.delete('/:userFarmId', authenticate, deleteUserFarm);

// Get UserFarm associations by user ID
router.get('/user/:userId', authenticate, getUserFarmsByUserId);

// Get UserFarm associations by farm ID
router.get('/farm/:farmId', authenticate, isAdmin, getUserFarmsByFarmId);

export default router;