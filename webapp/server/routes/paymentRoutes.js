import express from 'express';
import {
  getFarmInvoices,
  getInvoiceById,
  getFarmPaymentMethods,
  getPaymentMethod,
  getInvoiceByIdOnly
} from '../controllers/paymentController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get a single invoice by ID without requiring a farm ID (matches UUID pattern)
router.get('/invoices/:invoiceId([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})', authenticate, getInvoiceByIdOnly);

// Get a single invoice by ID for a farm
router.get('/invoices/:farmId/:invoiceId', authenticate, getInvoiceById);

// Get all invoices for a farm
router.get('/invoices/:farmId', authenticate, getFarmInvoices);

// Get a specific payment method by ID
router.get('/methods/method/:paymentMethodId', authenticate, getPaymentMethod);

// Get a specific payment method by ID (alternative route)
router.get('/methods/:paymentMethodId([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})', authenticate, getPaymentMethod);

// Get all payment methods for a farm
router.get('/methods/:farmId', authenticate, getFarmPaymentMethods);

export default router;
