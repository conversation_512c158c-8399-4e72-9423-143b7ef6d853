import twilio from 'twilio';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Twilio client
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER;

let client;

// Initialize the Twilio client if credentials are available
if (accountSid && authToken) {
  client = twilio(accountSid, authToken);
  console.log('Twilio client initialized');
} else {
  console.warn('Twilio credentials not found in environment variables');
}

/**
 * Send an SMS message
 * @param {string} to - Recipient phone number
 * @param {string} body - Message body
 * @returns {Promise} - Twilio message object
 */
export const sendSMS = async (to, body) => {
  if (!client) {
    throw new Error('Twilio client not initialized');
  }

  try {
    const message = await client.messages.create({
      body,
      from: twilioPhoneNumber,
      to
    });

    console.log(`SMS sent to ${to}, SID: ${message.sid}`);
    return message;
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw error;
  }
};

/**
 * Validate a phone number
 * @param {string} phoneNumber - Phone number to validate
 * @returns {string} - Formatted phone number or null if invalid
 */
export const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return null;

  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');

  // Check if it's a valid US number (10 digits)
  if (digitsOnly.length === 10) {
    return `+1${digitsOnly}`; // Add US country code
  }

  // Check if it already has a country code
  if (digitsOnly.length > 10 && digitsOnly.startsWith('1')) {
    return `+${digitsOnly}`;
  }

  // Return as is if it already has a plus sign
  if (phoneNumber.startsWith('+')) {
    return phoneNumber;
  }

  return null;
};

/**
 * Parse a media URL from Twilio
 * @param {string} mediaUrl - Twilio media URL
 * @returns {Promise<Buffer>} - Media content as buffer
 */
export const getMediaContent = async (mediaUrl) => {
  if (!client) {
    throw new Error('Twilio client not initialized');
  }

  try {
    // Twilio requires authentication to access media
    const response = await fetch(mediaUrl, {
      headers: {
        Authorization: `Basic ${Buffer.from(`${accountSid}:${authToken}`).toString('base64')}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch media: ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('Error getting media content:', error);
    throw error;
  }
};

/**
 * Generate a random verification code
 * @param {number} length - Length of the code (default: 6)
 * @returns {string} - Random numeric code
 */
export const generateVerificationCode = (length = 6) => {
  let code = '';
  for (let i = 0; i < length; i++) {
    code += Math.floor(Math.random() * 10).toString();
  }
  return code;
};

/**
 * Send a verification code via SMS
 * @param {string} phoneNumber - Phone number to send the code to
 * @param {string} code - Verification code
 * @returns {Promise} - Twilio message object
 */
export const sendVerificationCode = async (phoneNumber, code) => {
  const formattedNumber = validatePhoneNumber(phoneNumber);
  if (!formattedNumber) {
    throw new Error('Invalid phone number');
  }

  const message = `Your verification code is: ${code}. This code will expire in 10 minutes.`;
  return await sendSMS(formattedNumber, message);
};

/**
 * Send a 2FA code via SMS
 * @param {string} phoneNumber - Phone number to send the code to
 * @param {string} code - 2FA code
 * @returns {Promise} - Twilio message object
 */
export const send2FACode = async (phoneNumber, code) => {
  const formattedNumber = validatePhoneNumber(phoneNumber);
  if (!formattedNumber) {
    throw new Error('Invalid phone number');
  }

  const message = `Your authentication code is: ${code}. This code will expire in 5 minutes.`;
  return await sendSMS(formattedNumber, message);
};

export default {
  sendSMS,
  validatePhoneNumber,
  getMediaContent,
  generateVerificationCode,
  sendVerificationCode,
  send2FACode
};
