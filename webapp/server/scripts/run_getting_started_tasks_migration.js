import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runGettingStartedTasksMigration() {
  try {
    console.log('Running migration to add getting started tasks...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_getting_started_tasks.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Getting started tasks migration completed successfully!');
  } catch (error) {
    console.error('Error running getting started tasks migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runGettingStartedTasksMigration();