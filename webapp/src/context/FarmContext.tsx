import { createContext, useState, useEffect, useCallback, ReactNode, useContext } from 'react';
import axios from 'axios';
import { API_URL, MAIN_DOMAIN } from '../config';

// Define the shape of our Farm object
interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  UserFarm: {
    role: string;
  };
}

export interface Farm {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  tax_id: string;
  created_at: string;
  updated_at: string;
  subdomain?: string;
  Users?: User[];
}

// Define the shape of our context
interface FarmContextType {
  currentFarm: Farm | null;
  farms: Farm[];
  loading: boolean;
  error: string | null;
  fetchFarms: () => Promise<void>;
  setCurrentFarm: (farm: Farm) => void;
  clearCurrentFarm: () => void;
  inviteUserToFarm: (farmId: string, email: string, role: string) => Promise<void>;
  clearError: () => void;
  userRole?: string;
}

// Create the context with a default value
export const FarmContext = createContext<FarmContextType>({
  currentFarm: null,
  farms: [],
  loading: false,
  error: null,
  fetchFarms: async () => {},
  setCurrentFarm: () => {},
  clearCurrentFarm: () => {},
  inviteUserToFarm: async () => {},
  clearError: () => {},
  userRole: undefined,
});

// Provider component
export const FarmProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentFarm, setCurrentFarmState] = useState<Farm | null>(null);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize farm state from localStorage
  useEffect(() => {
    const storedCurrentFarm = localStorage.getItem('currentFarm');
    const storedToken = localStorage.getItem('token');

    if (storedCurrentFarm) {
      const farm = JSON.parse(storedCurrentFarm);
      setCurrentFarmState(farm);

      // Check if we need to redirect to a farm subdomain
      // Only redirect if the user is authenticated (has a valid token)
      if (farm.subdomain && storedToken) {
        // Get current hostname
        const hostname = window.location.hostname;
        const parts = hostname.split('.');
        const currentSubdomain = parts.length > 2 ? parts[0] : null;

        // Get the user from localStorage
        const storedUser = localStorage.getItem('user');
        const user = storedUser ? JSON.parse(storedUser) : null;
        const isGlobalAdmin = user?.is_global_admin || false;

        // If we're not already on the correct subdomain, redirect
        // But don't redirect global admins from the app subdomain
        if (currentSubdomain !== farm.subdomain && !(isGlobalAdmin && currentSubdomain === 'app')) {
          window.location.href = `https://${farm.subdomain}.${MAIN_DOMAIN}${window.location.pathname}`;
        }
      }
    }
  }, []);

  // Fetch all farms for the current user
  const fetchFarms = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the user ID from localStorage
      const storedUser = localStorage.getItem('user');
      if (!storedUser) {
        throw new Error('User not authenticated');
      }

      const user = JSON.parse(storedUser);
      const response = await axios.get(`${API_URL}/farms/user/${user.id}`);

      setFarms(response.data.farms || []);

      // If there's no current farm but we have farms, set the first one as current
      if (!currentFarm && response.data.farms && response.data.farms.length > 0) {
        setCurrentFarm(response.data.farms[0]);
      }
    } catch (err: unknown) {
      console.error('Error fetching farms:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Failed to load farms');
      } else {
        setError('Failed to load farms');
      }
    } finally {
      setLoading(false);
    }
  }, [currentFarm]);

  // Set current farm
  const setCurrentFarm = (farm: Farm) => {
    setCurrentFarmState(farm);
    localStorage.setItem('currentFarm', JSON.stringify(farm));

    // Check if we need to redirect to a farm subdomain
    // Only redirect if the user is authenticated (has a valid token)
    const storedToken = localStorage.getItem('token');
    if (farm.subdomain && storedToken) {
      // Get current hostname
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const currentSubdomain = parts.length > 2 ? parts[0] : null;

      // Get the user from localStorage
      const storedUser = localStorage.getItem('user');
      const userObj = storedUser ? JSON.parse(storedUser) : null;
      const isGlobalAdmin = userObj?.is_global_admin || false;

      // If we're not already on the correct subdomain, redirect
      // But don't redirect global admins from the app subdomain
      if (currentSubdomain !== farm.subdomain && !(isGlobalAdmin && currentSubdomain === 'app')) {
        window.location.href = `https://${farm.subdomain}.${MAIN_DOMAIN}${window.location.pathname}`;
      }
    }
  };

  // Clear current farm
  const clearCurrentFarm = () => {
    setCurrentFarmState(null);
    localStorage.removeItem('currentFarm');
  };

  // Invite user to farm
  const inviteUserToFarm = async (farmId: string, email: string, role: string) => {
    try {
      setLoading(true);
      setError(null);

      await axios.post(`${API_URL}/farms/${farmId}/invite`, { email, role });

      // Refresh farms list to get updated users
      await fetchFarms();
    } catch (err: unknown) {
      console.error('Error inviting user to farm:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.error || 'Failed to invite user');
      } else {
        setError('Failed to invite user');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Context value
  const contextValue: FarmContextType = {
    currentFarm,
    farms,
    loading,
    error,
    fetchFarms,
    setCurrentFarm,
    clearCurrentFarm,
    inviteUserToFarm,
    clearError,
  };

  return (
    <FarmContext.Provider value={contextValue}>
      {children}
    </FarmContext.Provider>
  );
};

// Custom hook to use the Farm context
export const useFarm = () => {
  const context = useContext(FarmContext);
  if (context === undefined) {
    throw new Error('useFarm must be used within a FarmProvider');
  }

  // Determine user role in the current farm
  let userRole: string | undefined = undefined;
  if (context.currentFarm && context.currentFarm.Users) {
    // Get the user from localStorage
    const storedUser = localStorage.getItem('user');
    const user = storedUser ? JSON.parse(storedUser) : null;

    if (user) {
      // Find the user in the current farm's Users array
      const farmUser = context.currentFarm.Users.find(u => u.id === user.id);
      if (farmUser && farmUser.UserFarm) {
        userRole = farmUser.UserFarm.role;
      }
    }
  }

  // Add selectedFarm as an alias for currentFarm for compatibility
  // and include userRole
  return {
    ...context,
    selectedFarm: context.currentFarm,
    userRole
  };
};
