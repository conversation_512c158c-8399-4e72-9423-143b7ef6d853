import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import FileManager from '../../components/filemanager/FileManager';
import { FileItem } from '../../components/filemanager/FileManagerTypes';

const FileManagerPage: React.FC = () => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [initialFolderId, setInitialFolderId] = useState<string | null>(null);

  // Get the folder ID from the URL query parameters on initial load
  useEffect(() => {
    const folderId = searchParams.get('folder');
    setInitialFolderId(folderId);
  }, [searchParams]);

  // Handle file open
  const handleFileOpen = (file: FileItem) => {
    navigate(`/documents/view/${file.id}`);
  };

  // Handle folder open - update URL with folder ID
  const handleFolderOpen = (folder: FileItem) => {
    setSearchParams({ folder: folder.id });
  };

  // Handle navigation to root or parent folder
  const handleFolderNavigation = (folderId: string | null) => {
    if (folderId === null) {
      // Remove folder parameter when navigating to root
      searchParams.delete('folder');
      setSearchParams(searchParams);
    } else {
      setSearchParams({ folder: folderId });
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">File Manager</h1>
      </div>

      <div className="bg-white rounded-lg shadow">
        <FileManager 
          farmId={currentFarm?.id || user?.farm_id || ''} 
          onFileOpen={handleFileOpen}
          onFolderOpen={handleFolderOpen}
          initialFolderId={initialFolderId}
          onFolderNavigation={handleFolderNavigation}
        />
      </div>
    </Layout>
  );
};

export default FileManagerPage;
