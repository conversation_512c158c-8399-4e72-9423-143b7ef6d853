import { Routes, Route } from 'react-router-dom';
import SustainabilityDashboard from './SustainabilityDashboard';
import CarbonFootprintCalculator from './CarbonFootprintCalculator';
import SustainablePractices from './SustainablePractices';
import CertificationManagement from './CertificationManagement';
import EnvironmentalImpactReports from './EnvironmentalImpactReports';

const SustainabilityRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<SustainabilityDashboard />} />
      <Route path="/carbon-footprint/*" element={<CarbonFootprintCalculator />} />
      <Route path="/practices/*" element={<SustainablePractices />} />
      <Route path="/certifications/*" element={<CertificationManagement />} />
      <Route path="/impact/*" element={<EnvironmentalImpactReports />} />
    </Routes>
  );
};

export default SustainabilityRoutes;
