import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createCertification, 
  getCertifications, 
  deleteCertification,
  updateCertification
} from '../../services/sustainabilityService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const CertificationManagement: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [certifications, setCertifications] = useState<any[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    certifyingBody: '',
    issueDate: format(new Date(), 'yyyy-MM-dd'),
    expirationDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd'),
    status: 'pending',
    documentUrl: '',
    notes: ''
  });

  useEffect(() => {
    if (currentFarm?.id) {
      fetchCertifications();
    }
  }, [currentFarm]);

  const fetchCertifications = async () => {
    try {
      setIsLoading(true);
      const data = await getCertifications(currentFarm!.id);
      setCertifications(data);
    } catch (error) {
      console.error('Error fetching certifications:', error);
      toast.error('Failed to fetch certifications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleEdit = (certification: any) => {
    setEditingId(certification.id);
    setFormData({
      name: certification.name,
      certifyingBody: certification.certifying_body,
      issueDate: format(new Date(certification.issue_date), 'yyyy-MM-dd'),
      expirationDate: format(new Date(certification.expiration_date), 'yyyy-MM-dd'),
      status: certification.status,
      documentUrl: certification.document_url || '',
      notes: certification.notes || ''
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({
      name: '',
      certifyingBody: '',
      issueDate: format(new Date(), 'yyyy-MM-dd'),
      expirationDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd'),
      status: 'pending',
      documentUrl: '',
      notes: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    const certificationData = {
      farmId: currentFarm.id,
      ...formData
    };

    try {
      setIsLoading(true);

      if (editingId) {
        // Update existing certification
        await updateCertification(editingId, certificationData);
        toast.success('Certification updated successfully');
        setEditingId(null);
      } else {
        // Create new certification
        await createCertification(certificationData);
        toast.success('Certification added successfully');
      }

      setFormData({
        name: '',
        certifyingBody: '',
        issueDate: format(new Date(), 'yyyy-MM-dd'),
        expirationDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd'),
        status: 'pending',
        documentUrl: '',
        notes: ''
      });
      fetchCertifications();
    } catch (error) {
      console.error('Error with certification:', error);
      toast.error(editingId ? 'Failed to update certification' : 'Failed to add certification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this certification?')) {
      try {
        setIsLoading(true);
        await deleteCertification(id);
        toast.success('Certification deleted successfully');
        fetchCertifications();
      } catch (error) {
        console.error('Error deleting certification:', error);
        toast.error('Failed to delete certification');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'pending': 'Pending',
      'active': 'Active',
      'expired': 'Expired'
    };
    return statuses[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'active': 'bg-green-100 text-green-800',
      'expired': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const isExpired = (expirationDate: string) => {
    return new Date(expirationDate) < new Date();
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Certification Management</h1>
        <button
          onClick={() => navigate('/sustainability')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {editingId ? 'Edit Certification' : 'Add Certification'}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Certification Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="certifyingBody" className="block text-sm font-medium text-gray-700 mb-1">
                Certifying Body
              </label>
              <input
                type="text"
                id="certifyingBody"
                name="certifyingBody"
                value={formData.certifyingBody}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700 mb-1">
                Issue Date
              </label>
              <input
                type="date"
                id="issueDate"
                name="issueDate"
                value={formData.issueDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-1">
                Expiration Date
              </label>
              <input
                type="date"
                id="expirationDate"
                name="expirationDate"
                value={formData.expirationDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="pending">Pending</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="documentUrl" className="block text-sm font-medium text-gray-700 mb-1">
                Document URL (optional)
              </label>
              <input
                type="url"
                id="documentUrl"
                name="documentUrl"
                value={formData.documentUrl}
                onChange={handleInputChange}
                placeholder="https://example.com/document.pdf"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
              >
                {isLoading 
                  ? (editingId ? 'Updating...' : 'Adding...') 
                  : (editingId ? 'Update Certification' : 'Add Certification')
                }
              </button>
              {editingId && (
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Certifications</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && certifications.length === 0 && (
            <p className="text-gray-500">No certifications found.</p>
          )}

          {!isLoading && certifications.length > 0 && (
            <div className="space-y-4">
              {certifications.map((certification) => (
                <div key={certification.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{certification.name}</h3>
                      <p className="text-sm text-gray-500 mb-2">
                        {certification.certifying_body} • 
                        Issued: {new Date(certification.issue_date).toLocaleDateString()} • 
                        Expires: {new Date(certification.expiration_date).toLocaleDateString()}
                      </p>
                      <span 
                        className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                          isExpired(certification.expiration_date) 
                            ? getStatusColor('expired') 
                            : getStatusColor(certification.status)
                        }`}
                      >
                        {isExpired(certification.expiration_date) ? 'Expired' : getStatusLabel(certification.status)}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      {certification.document_url && (
                        <a
                          href={certification.document_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Document
                        </a>
                      )}
                      <button
                        onClick={() => handleEdit(certification)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(certification.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  {certification.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{certification.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CertificationManagement;
