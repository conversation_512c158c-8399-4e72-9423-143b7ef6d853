import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { calculateCarbonFootprint, getCarbonFootprints, deleteCarbonFootprint, updateCarbonFootprint } from '../../services/sustainabilityService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const CarbonFootprintCalculator: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [footprints, setFootprints] = useState<any[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    calculationDate: format(new Date(), 'yyyy-MM-dd'),
    fuelEmissions: '',
    electricityEmissions: '',
    fertilizerEmissions: '',
    livestockEmissions: '',
    wasteEmissions: '',
    notes: ''
  });

  useEffect(() => {
    if (currentFarm?.id) {
      fetchFootprints();
    }
  }, [currentFarm]);

  const fetchFootprints = async () => {
    try {
      setIsLoading(true);
      const data = await getCarbonFootprints(currentFarm!.id);
      setFootprints(data);
    } catch (error) {
      console.error('Error fetching carbon footprints:', error);
      toast.error('Failed to fetch carbon footprints');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleEdit = (footprint: any) => {
    setEditingId(footprint.id);
    setFormData({
      calculationDate: format(new Date(footprint.calculation_date), 'yyyy-MM-dd'),
      fuelEmissions: footprint.fuel_emissions.toString(),
      electricityEmissions: footprint.electricity_emissions.toString(),
      fertilizerEmissions: footprint.fertilizer_emissions.toString(),
      livestockEmissions: footprint.livestock_emissions.toString(),
      wasteEmissions: footprint.waste_emissions.toString(),
      notes: footprint.notes || ''
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({
      calculationDate: format(new Date(), 'yyyy-MM-dd'),
      fuelEmissions: '',
      electricityEmissions: '',
      fertilizerEmissions: '',
      livestockEmissions: '',
      wasteEmissions: '',
      notes: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    const processedData = {
      farmId: currentFarm.id,
      ...formData,
      fuelEmissions: parseFloat(formData.fuelEmissions) || 0,
      electricityEmissions: parseFloat(formData.electricityEmissions) || 0,
      fertilizerEmissions: parseFloat(formData.fertilizerEmissions) || 0,
      livestockEmissions: parseFloat(formData.livestockEmissions) || 0,
      wasteEmissions: parseFloat(formData.wasteEmissions) || 0,
    };

    try {
      setIsLoading(true);

      if (editingId) {
        // Update existing footprint
        await updateCarbonFootprint(editingId, processedData);
        toast.success('Carbon footprint updated successfully');
        setEditingId(null);
      } else {
        // Create new footprint
        await calculateCarbonFootprint(processedData);
        toast.success('Carbon footprint calculated successfully');
      }

      setFormData({
        calculationDate: format(new Date(), 'yyyy-MM-dd'),
        fuelEmissions: '',
        electricityEmissions: '',
        fertilizerEmissions: '',
        livestockEmissions: '',
        wasteEmissions: '',
        notes: ''
      });
      fetchFootprints();
    } catch (error) {
      console.error('Error with carbon footprint:', error);
      toast.error(editingId ? 'Failed to update carbon footprint' : 'Failed to calculate carbon footprint');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this carbon footprint record?')) {
      try {
        setIsLoading(true);
        await deleteCarbonFootprint(id);
        toast.success('Carbon footprint deleted successfully');
        fetchFootprints();
      } catch (error) {
        console.error('Error deleting carbon footprint:', error);
        toast.error('Failed to delete carbon footprint');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const calculateTotal = () => {
    return (
      parseFloat(formData.fuelEmissions) || 0 +
      parseFloat(formData.electricityEmissions) || 0 +
      parseFloat(formData.fertilizerEmissions) || 0 +
      parseFloat(formData.livestockEmissions) || 0 +
      parseFloat(formData.wasteEmissions) || 0
    ).toFixed(2);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Carbon Footprint Calculator</h1>
        <button
          onClick={() => navigate('/sustainability')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {editingId ? 'Edit Carbon Footprint' : 'Calculate Carbon Footprint'}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="calculationDate" className="block text-sm font-medium text-gray-700 mb-1">
                Calculation Date
              </label>
              <input
                type="date"
                id="calculationDate"
                name="calculationDate"
                value={formData.calculationDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="fuelEmissions" className="block text-sm font-medium text-gray-700 mb-1">
                Fuel Emissions (CO2e tons)
              </label>
              <input
                type="number"
                id="fuelEmissions"
                name="fuelEmissions"
                value={formData.fuelEmissions}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="electricityEmissions" className="block text-sm font-medium text-gray-700 mb-1">
                Electricity Emissions (CO2e tons)
              </label>
              <input
                type="number"
                id="electricityEmissions"
                name="electricityEmissions"
                value={formData.electricityEmissions}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="fertilizerEmissions" className="block text-sm font-medium text-gray-700 mb-1">
                Fertilizer Emissions (CO2e tons)
              </label>
              <input
                type="number"
                id="fertilizerEmissions"
                name="fertilizerEmissions"
                value={formData.fertilizerEmissions}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="livestockEmissions" className="block text-sm font-medium text-gray-700 mb-1">
                Livestock Emissions (CO2e tons)
              </label>
              <input
                type="number"
                id="livestockEmissions"
                name="livestockEmissions"
                value={formData.livestockEmissions}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="wasteEmissions" className="block text-sm font-medium text-gray-700 mb-1">
                Waste Emissions (CO2e tons)
              </label>
              <input
                type="number"
                id="wasteEmissions"
                name="wasteEmissions"
                value={formData.wasteEmissions}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="mb-6 p-4 bg-gray-100 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Total Emissions</h3>
              <p className="text-2xl font-bold text-green-600">{calculateTotal()} CO2e tons</p>
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
              >
                {isLoading 
                  ? (editingId ? 'Updating...' : 'Calculating...') 
                  : (editingId ? 'Update Carbon Footprint' : 'Calculate Carbon Footprint')
                }
              </button>
              {editingId && (
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Carbon Footprint History</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && footprints.length === 0 && (
            <p className="text-gray-500">No carbon footprint records found.</p>
          )}

          {!isLoading && footprints.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Emissions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {footprints.map((footprint) => (
                    <tr key={footprint.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(footprint.calculation_date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {footprint.total_emissions} CO2e tons
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-3">
                          <button
                            onClick={() => handleEdit(footprint)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(footprint.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CarbonFootprintCalculator;
