import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createSustainablePractice, 
  getSustainablePractices, 
  deleteSustainablePractice,
  updateSustainablePractice
} from '../../services/sustainabilityService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const SustainablePractices: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [practices, setPractices] = useState<any[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'soil-management',
    implementationDate: format(new Date(), 'yyyy-MM-dd'),
    status: 'planned',
    impact: '',
    notes: ''
  });

  useEffect(() => {
    if (currentFarm?.id) {
      fetchPractices();
    }
  }, [currentFarm]);

  const fetchPractices = async () => {
    try {
      setIsLoading(true);
      const data = await getSustainablePractices(currentFarm!.id);
      setPractices(data);
    } catch (error) {
      console.error('Error fetching sustainable practices:', error);
      toast.error('Failed to fetch sustainable practices');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleEdit = (practice: any) => {
    setEditingId(practice.id);
    setFormData({
      name: practice.name,
      description: practice.description,
      category: practice.category,
      implementationDate: format(new Date(practice.implementation_date), 'yyyy-MM-dd'),
      status: practice.status,
      impact: practice.impact,
      notes: practice.notes || ''
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({
      name: '',
      description: '',
      category: 'soil-management',
      implementationDate: format(new Date(), 'yyyy-MM-dd'),
      status: 'planned',
      impact: '',
      notes: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    const practiceData = {
      farmId: currentFarm.id,
      ...formData
    };

    try {
      setIsLoading(true);

      if (editingId) {
        // Update existing practice
        await updateSustainablePractice(editingId, practiceData);
        toast.success('Sustainable practice updated successfully');
        setEditingId(null);
      } else {
        // Create new practice
        await createSustainablePractice(practiceData);
        toast.success('Sustainable practice added successfully');
      }

      setFormData({
        name: '',
        description: '',
        category: 'soil-management',
        implementationDate: format(new Date(), 'yyyy-MM-dd'),
        status: 'planned',
        impact: '',
        notes: ''
      });
      fetchPractices();
    } catch (error) {
      console.error('Error with sustainable practice:', error);
      toast.error(editingId ? 'Failed to update sustainable practice' : 'Failed to add sustainable practice');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this sustainable practice?')) {
      try {
        setIsLoading(true);
        await deleteSustainablePractice(id);
        toast.success('Sustainable practice deleted successfully');
        fetchPractices();
      } catch (error) {
        console.error('Error deleting sustainable practice:', error);
        toast.error('Failed to delete sustainable practice');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      'soil-management': 'Soil Management',
      'water-conservation': 'Water Conservation',
      'biodiversity': 'Biodiversity',
      'energy-efficiency': 'Energy Efficiency',
      'waste-reduction': 'Waste Reduction',
      'carbon-sequestration': 'Carbon Sequestration',
      'other': 'Other'
    };
    return categories[category] || category;
  };

  const getStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'planned': 'Planned',
      'in-progress': 'In Progress',
      'implemented': 'Implemented'
    };
    return statuses[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'planned': 'bg-yellow-100 text-yellow-800',
      'in-progress': 'bg-blue-100 text-blue-800',
      'implemented': 'bg-green-100 text-green-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Sustainable Practices</h1>
        <button
          onClick={() => navigate('/sustainability')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {editingId ? 'Edit Sustainable Practice' : 'Add Sustainable Practice'}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Practice Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="soil-management">Soil Management</option>
                <option value="water-conservation">Water Conservation</option>
                <option value="biodiversity">Biodiversity</option>
                <option value="energy-efficiency">Energy Efficiency</option>
                <option value="waste-reduction">Waste Reduction</option>
                <option value="carbon-sequestration">Carbon Sequestration</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="implementationDate" className="block text-sm font-medium text-gray-700 mb-1">
                Implementation Date
              </label>
              <input
                type="date"
                id="implementationDate"
                name="implementationDate"
                value={formData.implementationDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="planned">Planned</option>
                <option value="in-progress">In Progress</option>
                <option value="implemented">Implemented</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="impact" className="block text-sm font-medium text-gray-700 mb-1">
                Expected Impact
              </label>
              <textarea
                id="impact"
                name="impact"
                value={formData.impact}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
              >
                {isLoading 
                  ? (editingId ? 'Updating...' : 'Adding...') 
                  : (editingId ? 'Update Sustainable Practice' : 'Add Sustainable Practice')
                }
              </button>
              {editingId && (
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Sustainable Practices</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && practices.length === 0 && (
            <p className="text-gray-500">No sustainable practices found.</p>
          )}

          {!isLoading && practices.length > 0 && (
            <div className="space-y-4">
              {practices.map((practice) => (
                <div key={practice.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{practice.name}</h3>
                      <p className="text-sm text-gray-500 mb-2">
                        {getCategoryLabel(practice.category)} • 
                        Implementation: {new Date(practice.implementation_date).toLocaleDateString()}
                      </p>
                      <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getStatusColor(practice.status)}`}>
                        {getStatusLabel(practice.status)}
                      </span>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleEdit(practice)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(practice.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <p className="mt-2 text-gray-700">{practice.description}</p>
                  <div className="mt-2">
                    <p className="text-sm font-medium text-gray-700">Expected Impact:</p>
                    <p className="text-sm text-gray-600">{practice.impact}</p>
                  </div>
                  {practice.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{practice.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default SustainablePractices;
