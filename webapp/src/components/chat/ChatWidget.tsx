import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useChat } from '../../context/ChatContext';
import ChatIcon from './icons/ChatIcon';
import ChatWindow from './ChatWindow';
import ChatBar from './ChatBar';
import NotificationBadge from './NotificationBadge';
import { useOnClickOutside } from '../../hooks/useOnClickOutside';

interface ChatWidgetProps {
  // Optional props for customization
  position?: 'bottom-right' | 'bottom-left';
  iconSize?: number;
}

/**
 * Chat widget component that displays a chat icon in the corner of the screen
 * and opens a chat window when clicked
 */
const ChatWidget: React.FC<ChatWidgetProps> = ({
  position = 'bottom-right',
  iconSize = 24
}) => {
  const { user } = useAuth();
  const { conversations } = useChat();
  const [isOpen, setIsOpen] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const widgetRef = useRef<HTMLDivElement>(null);

  // Calculate total unread messages
  const totalUnreadCount = useMemo(() => {
    return conversations.reduce((total, conversation) => {
      return total + (conversation.unread_count || 0);
    }, 0);
  }, [conversations]);

  // Close the chat window when clicking outside
  useOnClickOutside(widgetRef, () => {
    if (isOpen && !isPinned) {
      setIsOpen(false);
    }
  });

  // Load pinned state from localStorage
  useEffect(() => {
    const savedPinned = localStorage.getItem('chatWidgetPinned');
    if (savedPinned) {
      setIsPinned(savedPinned === 'true');
      if (savedPinned === 'true') {
        setIsOpen(true);
      }
    }
  }, []);

  // Save pinned state to localStorage
  useEffect(() => {
    localStorage.setItem('chatWidgetPinned', isPinned.toString());
  }, [isPinned]);

  // Toggle the chat window
  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  // Toggle the pinned state
  const togglePin = () => {
    setIsPinned(!isPinned);
  };

  // Don't render if not authenticated
  if (!user) {
    return null;
  }

  return (
    <div 
      ref={widgetRef}
      className={`fixed z-50 ${
        position === 'bottom-right' ? 'right-4' : 'left-4'
      } ${
        isPinned ? 'bottom-0 w-full left-0 right-0' : 'bottom-4'
      }`}
    >
      {isPinned ? (
        <ChatBar 
          onClose={() => setIsOpen(false)}
          onUnpin={() => setIsPinned(false)}
        />
      ) : isOpen ? (
        <ChatWindow 
          onClose={() => setIsOpen(false)}
          onPin={togglePin}
          isPinned={isPinned}
        />
      ) : (
        <button
          onClick={toggleChat}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg flex items-center justify-center relative"
          aria-label="Open chat"
        >
          <ChatIcon size={iconSize} />
          <NotificationBadge count={totalUnreadCount} />
        </button>
      )}
    </div>
  );
};

export default ChatWidget;
