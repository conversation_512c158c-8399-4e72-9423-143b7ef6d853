/* Folder Explorer Styles */
.file-manager-folder-explorer {
  height: 100%;
  overflow-y: auto;
  user-select: none;
}

/* Prevent text selection in file manager */
.file-item, 
.folder-explorer-tree,
table.min-w-full th,
table.min-w-full td,
.bg-white.rounded-lg.shadow.relative {
  user-select: none;
}

.folder-explorer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.folder-explorer-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.refresh-button:hover {
  background-color: #F3F4F6;
}

.folder-explorer-tree {
  margin-top: 0.5rem;
}

.folder-explorer-item {
  margin-bottom: 0.25rem;
}

.folder-explorer-item-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
  cursor: pointer;
  border-radius: 0.25rem;
}

.folder-explorer-item-header:hover {
  background-color: #F3F4F6;
}

.folder-explorer-item-header.selected {
  background-color: #E5E7EB;
  font-weight: 500;
}

.folder-explorer-item-header.drop-target {
  background-color: #DBEAFE;
  border: 1px dashed #3B82F6;
}

.folder-explorer-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-right: 0.25rem;
  width: 1.5rem;
  text-align: center;
  color: #6B7280;
}

.folder-explorer-name {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-icon {
  margin-right: 0.5rem;
}

.folder-explorer-children {
  margin-left: 1rem;
}

.folder-explorer-loading,
.folder-explorer-error {
  padding: 1rem;
  color: #6B7280;
  text-align: center;
}

.folder-explorer-error {
  color: #EF4444;
}
