import { ReactNode } from 'react';

// File/Folder item type
export interface FileItem {
  id: string;
  name: string;
  description: string;
  type: 'file' | 'folder';
  file_path?: string;
  file_size?: number;
  file_type?: string;
  mime_type?: string;
  is_external?: boolean;
  external_source?: string | null;
  folder_id?: string | null;
  parent_folder_id?: string | null;
  farm_id: string;
  created_by?: string;
  uploaded_by?: string;
  created_at: string;
  updated_at: string;
  is_secure?: boolean;
  uploader?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

// Context menu item type
export interface ContextMenuItem {
  label: string;
  icon?: ReactNode;
  onClick: () => void;
  disabled?: boolean;
  divider?: boolean;
  danger?: boolean;
}

// Breadcrumb item type
export interface BreadcrumbItem {
  id: string | null;
  name: string;
}

// File manager view mode
export type ViewMode = 'grid' | 'list';

// Drag item types
export const ItemTypes = {
  FILE: 'file',
  FOLDER: 'folder'
};

// Drag source item
export interface DragItem {
  id: string;
  type: 'file' | 'folder';
}

// Drop result
export interface DropResult {
  dropEffect: string;
}

// File manager props
export interface FileManagerProps {
  farmId: string;
  onFileOpen?: (file: FileItem) => void;
  onFolderOpen?: (folder: FileItem) => void;
  initialFolderId?: string | null;
  onFolderNavigation?: (folderId: string | null, folderName: string) => void;
  className?: string;
}

// File manager grid props
export interface FileManagerGridProps {
  items: FileItem[];
  selectedItems: string[];
  focusedItemIndex: number;
  onSelect: (itemId: string, isMultiSelect: boolean) => void;
  onDoubleClick: (item: FileItem) => void;
  onMove: (itemIds: string[], targetFolderId: string | null) => void;
  currentFolder: string | null;
  onManagePermissions: (item: FileItem) => void;
  onShare?: (item: FileItem) => void;
  onCopy: (items: FileItem[]) => void;
  onCut: (items: FileItem[]) => void;
  onPaste: (targetFolderId: string | null) => Promise<void>;
  canPaste: boolean;
  registerItemRef: (itemId: string, element: HTMLElement | null) => void;
}

// File manager list props
export interface FileManagerListProps {
  items: FileItem[];
  selectedItems: string[];
  focusedItemIndex: number;
  onSelect: (itemId: string, isMultiSelect: boolean) => void;
  onDoubleClick: (item: FileItem) => void;
  onSort: (field: string) => void;
  sortBy: string;
  sortDirection: 'asc' | 'desc';
  onMove: (itemIds: string[], targetFolderId: string | null) => void;
  currentFolder: string | null;
  onManagePermissions: (item: FileItem) => void;
  onShare?: (item: FileItem) => void;
  onCopy: (items: FileItem[]) => void;
  onCut: (items: FileItem[]) => void;
  onPaste: (targetFolderId: string | null) => Promise<void>;
  canPaste: boolean;
  registerItemRef: (itemId: string, element: HTMLElement | null) => void;
}
