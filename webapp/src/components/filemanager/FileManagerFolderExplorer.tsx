import React, { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { useDrop } from 'react-dnd';
import { API_URL } from '../../config';
import { FileItem, ItemTypes } from './FileManagerTypes';
import ContextMenu from '../ContextMenu';

// Define the folder hierarchy item type
interface FolderHierarchyItem {
  id: string;
  name: string;
  children: FolderHierarchyItem[];
}

// Define drag item type
interface DragItem {
  id: string;
  type: string;
}

// Define the component props
interface FileManagerFolderExplorerProps {
  farmId: string;
  currentFolder: string | null;
  onNavigate: (folderId: string | null, folderName: string) => void;
  onMove?: (itemIds: string[], targetFolderId: string | null) => void;
}

const FileManagerFolderExplorer: React.FC<FileManagerFolderExplorerProps> = ({
  farmId,
  currentFolder,
  onNavigate,
  onMove
}) => {
  // State
  const [folderHierarchy, setFolderHierarchy] = useState<FolderHierarchyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Initialize with 'root' expanded to show folders under root by default
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root']));

  // Fetch folder hierarchy
  const fetchFolderHierarchy = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${API_URL}/documents/farm/${farmId}/folder-hierarchy`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      const folders = response.data;
      setFolderHierarchy(folders);

      // Expand all root-level folders by default
      const rootFolderIds = folders.map(folder => folder.id);
      setExpandedFolders(prev => {
        const newSet = new Set(prev);
        // Always ensure 'root' is in the set
        newSet.add('root');
        // Add all root-level folder IDs
        rootFolderIds.forEach(id => newSet.add(id));
        return newSet;
      });
    } catch (err: any) {
      console.error('Error fetching folder hierarchy:', err);
      const errorMessage = err.response?.data?.error || 'Failed to load folder hierarchy. Please try again later.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [farmId]);

  // Fetch folder hierarchy on mount and when farmId changes
  useEffect(() => {
    fetchFolderHierarchy();
  }, [fetchFolderHierarchy]);

  // Toggle folder expansion
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  // Handle folder click
  const handleFolderClick = (folder: FolderHierarchyItem) => {
    // Just highlight the folder, don't navigate
    // This will be handled by the parent component
    // through the currentFolder prop
  };

  // Handle folder double click
  const handleFolderDoubleClick = (folder: FolderHierarchyItem, e?: React.MouseEvent) => {
    // Ensure the folder is expanded
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      newSet.add(folder.id);
      return newSet;
    });

    // Navigate to the folder
    onNavigate(folder.id, folder.name);

    // Prevent event bubbling
    e?.stopPropagation();
  };

  // Render folder item
  const renderFolderItem = (folder: FolderHierarchyItem, level: number = 0) => {
    const isExpanded = expandedFolders.has(folder.id);
    const isSelected = currentFolder === folder.id;
    const hasChildren = folder.children && folder.children.length > 0;
    const ref = useRef<HTMLDivElement>(null);

    // Set up drop target for folder
    const [{ isOver, canDrop }, drop] = useDrop({
      accept: [ItemTypes.FILE, ItemTypes.FOLDER],
      canDrop: (draggedItem: DragItem) => {
        // Can't drop on itself
        if (draggedItem.id === folder.id) return false;
        // Can't drop if onMove is not provided
        if (!onMove) return false;
        return true;
      },
      drop: (draggedItem: DragItem) => {
        if (onMove) {
          onMove([draggedItem.id], folder.id);
        }
        return { dropEffect: 'move' };
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    });

    // Apply drop ref
    drop(ref);

    // Define context menu items for this folder
    const folderContextMenuItems = [
      {
        label: 'Open Folder',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
            />
          </svg>
        ),
        onClick: () => handleFolderDoubleClick(folder, undefined)
      },
      {
        label: hasChildren ? (isExpanded ? 'Collapse' : 'Expand') : 'No Subfolders',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={hasChildren 
                ? (isExpanded 
                  ? "M5 15l7-7 7 7" 
                  : "M19 9l-7 7-7-7")
                : "M6 18L18 18M12 12v0"
              }
            />
          </svg>
        ),
        onClick: () => hasChildren && toggleFolder(folder.id),
        disabled: !hasChildren
      },
      {
        label: 'Create Subfolder',
        icon: (
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 13h6m-3-3v6m-9-6h9m-9 6h9"
            />
          </svg>
        ),
        onClick: () => {
          // Dispatch a custom event to show the create folder modal
          const event = new CustomEvent('createFolderInParent', { 
            detail: { parentFolderId: folder.id } 
          });
          document.dispatchEvent(event);
        }
      }
    ];

    return (
      <ContextMenu items={folderContextMenuItems}>
        <div key={folder.id} className="folder-explorer-item">
          <div 
            ref={ref}
            className={`folder-explorer-item-header ${isSelected ? 'selected' : ''} ${isOver && canDrop ? 'drop-target' : ''}`}
            style={{ paddingLeft: `${level * 16}px` }}
          >
            {hasChildren && (
              <button 
                className="folder-explorer-toggle"
                onClick={() => toggleFolder(folder.id)}
              >
                {isExpanded ? '▼' : '►'}
              </button>
            )}
            <div 
              className="folder-explorer-name"
              onClick={() => handleFolderClick(folder)}
              onDoubleClick={(e) => handleFolderDoubleClick(folder, e)}
            >
              <span className="folder-icon relative">
                📁
                {folder.is_secure && (
                  <span 
                    className="absolute -top-1 -right-1 text-red-500" 
                    title="Secure folder - cannot be shared with public link"
                  >
                    🔒
                  </span>
                )}
              </span>
              {folder.name}
            </div>
          </div>

          {isExpanded && hasChildren && (
            <div className="folder-explorer-children">
              {folder.children.map(child => renderFolderItem(child, level + 1))}
            </div>
          )}
        </div>
      </ContextMenu>
    );
  };

  // Handle root folder click
  const handleRootClick = () => {
    // Just highlight the root folder, don't navigate
    // This will be handled by the parent component
    // through the currentFolder prop
  };

  // Handle root folder double click
  const handleRootDoubleClick = (e?: React.MouseEvent) => {
    // Navigate to the root folder
    onNavigate(null, 'Root');

    // Prevent event bubbling
    e?.stopPropagation();
  };

  // Set up drop target for root folder
  const rootRef = useRef<HTMLDivElement>(null);
  const [{ isOver: isRootOver, canDrop: canDropRoot }, dropRoot] = useDrop({
    accept: [ItemTypes.FILE, ItemTypes.FOLDER],
    canDrop: (draggedItem: DragItem) => {
      // Can't drop if onMove is not provided
      if (!onMove) return false;
      return true;
    },
    drop: (draggedItem: DragItem) => {
      if (onMove) {
        onMove([draggedItem.id], null);
      }
      return { dropEffect: 'move' };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  // Apply drop ref to root folder
  dropRoot(rootRef);

  return (
    <div className="file-manager-folder-explorer">
      <div className="folder-explorer-header">
        <h3>Folders</h3>
        <button 
          className="refresh-button"
          onClick={fetchFolderHierarchy}
          title="Refresh folder hierarchy"
        >
          🔄
        </button>
      </div>

      {loading ? (
        <div className="folder-explorer-loading">Loading folders...</div>
      ) : error ? (
        <div className="folder-explorer-error">{error}</div>
      ) : (
        <div className="folder-explorer-tree">
          <ContextMenu items={[
            {
              label: 'Open Root Folder',
              icon: (
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                  />
                </svg>
              ),
              onClick: () => handleRootDoubleClick()
            },
            {
              label: expandedFolders.has('root') ? 'Collapse All' : 'Expand All',
              icon: (
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={expandedFolders.has('root') 
                      ? "M5 15l7-7 7 7" 
                      : "M19 9l-7 7-7-7"}
                  />
                </svg>
              ),
              onClick: () => toggleFolder('root')
            }
          ]}>
            <div 
              ref={rootRef}
              className={`folder-explorer-item folder-explorer-item-header ${currentFolder === null ? 'selected' : ''} ${isRootOver && canDropRoot ? 'drop-target' : ''}`}
              onClick={handleRootClick}
              onDoubleClick={(e) => handleRootDoubleClick(e)}
            >
              <button 
                className="folder-explorer-toggle"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder('root');
                }}
              >
                {expandedFolders.has('root') ? '▼' : '►'}
              </button>
              <span className="folder-icon">📁</span>
              Root
            </div>
          </ContextMenu>
          {expandedFolders.has('root') && folderHierarchy.map(folder => renderFolderItem(folder))}
        </div>
      )}
    </div>
  );
};

export default FileManagerFolderExplorer;
